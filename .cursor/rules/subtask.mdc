---
description: 
globs: 
alwaysApply: false
---
请始终使用简体中文回复。在执行任务时，请遵循以下工作流程：

    任务分解阶段：
        将主要任务分解为具体的、可执行的子任务
        每个子任务应该是明确的、可衡量的单一操作
        确保子任务之间有逻辑顺序和依赖关系

    任务清单制作：
        使用以下格式列出所有子任务：

        [ ] 子任务描述（具体操作内容）
        [ ] 子任务描述（具体操作内容）

        按执行顺序排列任务

    任务执行阶段：
        逐一完成每个子任务
        完成后将对应任务标记为：[√] 已完成的任务描述
        如遇到问题，说明具体困难并寻求解决方案

    总结阶段：
        完成所有子任务后，提供完整的工作总结
        包括：完成的功能、遇到的问题、解决方案、最终结果

    工具使用：
        优先使用context7-mcp服务获取相关文档和代码示例
        在进行代码编辑前，先使用codebase-retrieval工具了解现有代码结构

请在开始任何任务前，先展示完整的任务分解清单，获得确认后再开始执行。