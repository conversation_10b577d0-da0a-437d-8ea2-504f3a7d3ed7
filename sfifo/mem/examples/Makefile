# Makefile for SFIFO-HFIFO abstraction modules

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -g -pthread
INCLUDES = -I. -I../proxy

# VCS/Xcelium settings
VCS_INCLUDE = -I${VCS_HOME}/include
SHARED_FLAGS = -shared -fPIC -pthread -g
SVLIB_NAME = libsfifo_hfifo.so

# Source files
WRAPPER_SOURCES = ../proxy/mem_sfifo_wrapper.cpp
TEST_SOURCES = test/test_sfifo_hfifo.cpp
EXAMPLE_SOURCES = examples/sfifo_hfifo_usage_example.cpp
DPI_EXAMPLE_SOURCES = examples/sfifo_hfifo_dpi_example.cpp
ENHANCED_EXAMPLE_SOURCES = mem_sfifo_enhanced_tb.cpp

# SystemVerilog files
RTL_SOURCES = ../bfm/mem_sfifo_bfm.sv
EXAMPLE_TB = ./mem_sfifo_enhanced_tb.sv
DPI_TB = examples/sfifo_hfifo_dpi_tb.sv
SIMPLE_TB = examples/sfifo_hfifo_simple_tb.sv
ENHANCED_TB = ./mem_sfifo_enhanced_tb.sv

# Header files
HEADERS = ../proxy/mem_sfifo_client.h \
          ../proxy/mem_sfifo_wrapper.h

# Output directories
BUILD_DIR = build
TEST_DIR = $(BUILD_DIR)/test
EXAMPLE_DIR = $(BUILD_DIR)/examples
SIM_DIR = sim
VCS_DIR = $(SIM_DIR)/vcs
XLM_DIR = $(SIM_DIR)/xlm

# Debug options
VCD = 0
ifeq ($(VCD), 1)
	DUMP_VCD = +define+DUMP_VCD
else
	DUMP_VCD =
endif

FSDB = 0
ifeq ($(FSDB), 1)
	DUMP_FSDB = +define+DUMP_FSDB
else
	DUMP_FSDB =
endif

DEBUG_SW = 1
ifeq ($(DEBUG_SW), 1)
	DEBUG_SW_FLAG = -DDEBUG_SW
else
	DEBUG_SW_FLAG =
endif

DEBUG_HW = 1
ifeq ($(DEBUG_HW), 1)
	DEBUG_HW_FLAG = +define+DEBUG+VP_TEST
else
	DEBUG_HW_FLAG =
endif

HW_DEFINE_OPTS = $(DUMP_VCD) $(DEBUG_HW_FLAG) $(DUMP_FSDB)

# Targets
.PHONY: all clean test examples help vcs_comp vcs_run vcs_grun xlm_comp xlm_run xlm_grun cleanall create_dirs

all: test examples $(SVLIB_NAME)

# Create build directories
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

$(TEST_DIR): $(BUILD_DIR)
	mkdir -p $(TEST_DIR)

$(EXAMPLE_DIR): $(BUILD_DIR)
	mkdir -p $(EXAMPLE_DIR)

# Create simulation directories
create_dirs:
	mkdir -p $(VCS_DIR) $(XLM_DIR)

# DPI library compilation (using simplified version)
$(SVLIB_NAME): examples/sfifo_hfifo_simple_dpi.cpp
	$(CXX) -o $(SVLIB_NAME) examples/sfifo_hfifo_simple_dpi.cpp $(DEBUG_SW_FLAG) $(VCS_INCLUDE) $(INCLUDES) $(SHARED_FLAGS)

# Enhanced DPI library compilation
ENHANCED_SVLIB_NAME = libsfifo_hfifo_enhanced.so
$(ENHANCED_SVLIB_NAME): $(ENHANCED_EXAMPLE_SOURCES) $(WRAPPER_SOURCES) $(HEADERS)
	$(CXX) $(CXXFLAGS) -o $(ENHANCED_SVLIB_NAME) $(ENHANCED_EXAMPLE_SOURCES) $(WRAPPER_SOURCES) $(DEBUG_SW_FLAG) $(INCLUDES) $(VCS_INCLUDE) $(SHARED_FLAGS)

# Test target
test: $(TEST_DIR)/test_sfifo_hfifo
	@echo "Running SFIFO-HFIFO tests..."
	@$(TEST_DIR)/test_sfifo_hfifo

$(TEST_DIR)/test_sfifo_hfifo: $(TEST_SOURCES) $(HEADERS) | $(TEST_DIR)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $(TEST_SOURCES)

# Examples target (for standalone testing)
examples: $(EXAMPLE_DIR)/usage_example

$(EXAMPLE_DIR)/usage_example: $(EXAMPLE_SOURCES) $(WRAPPER_SOURCES) $(HEADERS) | $(EXAMPLE_DIR)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -o $@ $(EXAMPLE_SOURCES) $(WRAPPER_SOURCES)

# VCS targets (using simplified testbench)
vcs_simple_comp: create_dirs $(SIMPLE_TB)
	cd $(VCS_DIR) && vcs -full64 -kdb -sverilog -debug_access+all $(HW_DEFINE_OPTS) ../../$(SIMPLE_TB) -CFLAGS -g

vcs_simple_run: $(SVLIB_NAME)
	cd $(VCS_DIR) && ./simv -sv_lib ../../$(basename $(SVLIB_NAME)) -l vcs.log

vcs_simple_grun: $(SVLIB_NAME)
	cd $(VCS_DIR) && ./simv -gui -sv_lib ../../$(basename $(SVLIB_NAME)) -l vcs.log

# Enhanced VCS targets
vcs_enhanced_comp: create_dirs $(RTL_SOURCES) $(ENHANCED_TB)
	cd $(VCS_DIR) && vcs -full64 -kdb -sverilog -debug_access+all $(HW_DEFINE_OPTS) ../../$(RTL_SOURCES) ../../$(ENHANCED_TB) -CFLAGS -g

vcs_enhanced_run: $(ENHANCED_SVLIB_NAME)
	cd $(VCS_DIR) && ./simv -sv_lib ../../$(basename $(ENHANCED_SVLIB_NAME)) -l enhanced_vcs.log

vcs_enhanced_grun: $(ENHANCED_SVLIB_NAME)
	cd $(VCS_DIR) && ./simv -gui -sv_lib ../../$(basename $(ENHANCED_SVLIB_NAME)) -l enhanced_vcs.log

# Xcelium targets
xlm_comp: create_dirs $(RTL_SOURCES) $(DPI_TB)
	cd $(XLM_DIR) && xrun -c -sv ../../$(RTL_SOURCES) ../../$(DPI_TB) $(HW_DEFINE_OPTS) -access rwc -linedebug

xlm_run: $(SVLIB_NAME)
	cd $(XLM_DIR) && xrun -R -sv_lib ../../$(SVLIB_NAME)

xlm_grun: $(SVLIB_NAME)
	cd $(XLM_DIR) && xrun -R -gui -sv_lib ../../$(SVLIB_NAME) -l xlm.log

# Clean targets
clean:
	rm -rf $(BUILD_DIR) $(SIM_DIR) $(SVLIB_NAME) *.log *.fsdb

cleanall:
	rm -rf $(BUILD_DIR) $(SIM_DIR) $(SVLIB_NAME) *.log *.fsdb verdiLog csrc simv simv.daidir vc_hdrs.h ucli.key sysBusyPLog xcelium.d novas.*

# Help target
help:
	@echo "SFIFO-HFIFO Abstraction Makefile"
	@echo ""
	@echo "Available targets:"
	@echo "  all      - Build tests, examples and shared library"
	@echo "  test     - Build and run unit tests"
	@echo "  examples - Build usage examples (standalone)"
	@echo ""
	@echo "VCS targets (Simple):"
	@echo "  vcs_comp - Compile design with VCS (simple testbench)"
	@echo "  vcs_run  - Run simulation with VCS (simple testbench)"
	@echo "  vcs_grun - Run simulation with VCS GUI (simple testbench)"
	@echo ""
	@echo "VCS targets (Enhanced):"
	@echo "  enhanced_comp - Compile design with VCS (enhanced testbench)"
	@echo "  enhanced_run  - Run simulation with VCS (enhanced testbench)"
	@echo "  enhanced_grun - Run simulation with VCS GUI (enhanced testbench)"
	@echo ""
	@echo "Xcelium targets:"
	@echo "  xlm_comp - Compile design with Xcelium"
	@echo "  xlm_run  - Run simulation with Xcelium"
	@echo "  xlm_grun - Run simulation with Xcelium GUI"
	@echo ""
	@echo "Shared libraries:"
	@echo "  $(SVLIB_NAME) - Build simple DPI shared library"
	@echo "  $(ENHANCED_SVLIB_NAME) - Build enhanced DPI shared library"
	@echo ""
	@echo "Clean targets:"
	@echo "  clean    - Remove build artifacts and simulation files"
	@echo "  cleanall - Remove all generated files"
	@echo "  help     - Show this help message"
	@echo ""
	@echo "Files:"
	@echo "  Headers:"
	@echo "    verif/vpWrHwPageSync/src/cc/sfifo_hfifo_client.h    - Template client class"
	@echo "    verif/vpWrHwPageSync/src/cc/sfifo_hfifo_wrapper.h   - Wrapper interface"
	@echo "  Sources:"
	@echo "    verif/vpWrHwPageSync/src/cc/sfifo_hfifo_wrapper.cpp - Wrapper implementation"
	@echo "  SystemVerilog:"
	@echo "    rtl/sfifo_hfifo_hw.sv                               - Hardware modules"
	@echo "  Tests:"
	@echo "    test/test_sfifo_hfifo.cpp                           - Unit tests"
	@echo "  Examples:"
	@echo "    examples/sfifo_hfifo_usage_example.cpp              - C++ usage example"
	@echo "    examples/sfifo_hfifo_usage_example.sv               - SystemVerilog example"

# Check syntax
check-syntax:
	@echo "Checking C++ syntax..."
	$(CXX) $(CXXFLAGS) $(INCLUDES) -fsyntax-only $(WRAPPER_SOURCES)
	$(CXX) $(CXXFLAGS) $(INCLUDES) -fsyntax-only $(TEST_SOURCES)
	@echo "C++ syntax check passed!"

# Documentation target
docs:
	@echo "SFIFO-HFIFO Abstraction Documentation"
	@echo "======================================"
	@echo ""
	@echo "This project abstracts the SFIFO-HFIFO mechanism from hbm3_pc_bfm.sv"
	@echo "into reusable, optimized modules."
	@echo ""
	@echo "Key Features:"
	@echo "  • Batch processing optimization (1 -> N requests per transfer)"
	@echo "  • Template-based type safety"
	@echo "  • Configurable parameters"
	@echo "  • Clean separation of software/hardware concerns"
	@echo "  • Thread-safe operations"
	@echo ""
	@echo "Usage:"
	@echo "  1. Include sfifo_hfifo_client.h in your C++ code"
	@echo "  2. Instantiate sfifo_hfifo_integrated in your SystemVerilog"
	@echo "  3. Use SfifoHfifoWrapper for high-level management"
	@echo ""
	@echo "See README_SFIFO_HFIFO.md for detailed documentation."

# Performance test (if we had performance benchmarks)
perf-test:
	@echo "Performance comparison:"
	@echo "  Original: 1 request per polling cycle"
	@echo "  Optimized: Up to MAX_BATCH_SIZE requests per polling cycle"
	@echo "  Theoretical speedup: $(MAX_BATCH_SIZE)x (with MAX_BATCH_SIZE=4)"
	@echo ""
	@echo "Run 'make test' to verify functionality."

# Show file structure
structure:
	@echo "Project Structure:"
	@echo "├── verif/vpWrHwPageSync/src/cc/"
	@echo "│   ├── sfifo_hfifo_client.h          # Template client classes"
	@echo "│   ├── sfifo_hfifo_wrapper.h         # Wrapper interface"
	@echo "│   └── sfifo_hfifo_wrapper.cpp       # Wrapper implementation"
	@echo "├── rtl/"
	@echo "│   └── sfifo_hfifo_hw.sv             # Hardware modules"
	@echo "├── examples/"
	@echo "│   ├── sfifo_hfifo_usage_example.cpp # C++ usage example"
	@echo "│   └── sfifo_hfifo_usage_example.sv  # SystemVerilog example"
	@echo "├── test/"
	@echo "│   └── test_sfifo_hfifo.cpp          # Unit tests"
	@echo "├── README_SFIFO_HFIFO.md             # Documentation"
	@echo "└── Makefile                          # This file"
