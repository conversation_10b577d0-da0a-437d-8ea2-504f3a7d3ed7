/**
 * @file sfifo_hfifo_enhanced_tb.sv
 * @brief Enhanced SystemVerilog testbench with Reference Model and Event Communication
 *
 * This testbench demonstrates:
 * 1. Complete write-read-compare flow
 * 2. Event communication from software to hardware
 * 3. Automatic test completion detection
 * 4. Data integrity verification
 */

`timescale 1ns/1ps

// The sfifo_hfifo_hw module will be compiled separately

module mem_sfifo_enhanced_tb;

    // Clock and reset
    reg clk;
    reg rstn;

    // Test parameters
    parameter INSTANCE_ID = 0;
    parameter FIFO_DEPTH = 16;
    parameter ADDR_WIDTH = 64;
    parameter DATA_WIDTH = 256;
    parameter DATA_WIDTH_IN_BYTE = DATA_WIDTH/8;
    parameter POLLING_INTERVAL = 50;  // Fast polling for testing

    // Unified request interface signals
    wire req_valid;
    wire [1:0] req_type;  // 2'b00: READ, 2'b01: WRITE
    wire [ADDR_WIDTH-1:0] req_addr;
    wire [DATA_WIDTH-1:0] req_data;
    wire [DATA_WIDTH_IN_BYTE-1:0] req_mask;
    reg req_ready;

    wire fifo_empty, fifo_full;

    // Event FIFO interface (similar to read/write FIFOs)
    wire event_req_valid;
    wire [31:0] event_type;
    wire [63:0] event_data;
    wire [31:0] event_param;
    reg event_req_ready;

    // Event FIFO storage (similar to read/write FIFO implementation)
    parameter EVENT_FIFO_DEPTH = 8;
    parameter EVENT_FIFO_AWIDTH = $clog2(EVENT_FIFO_DEPTH);

    reg [31:0] event_type_fifo [0:EVENT_FIFO_DEPTH-1];
    reg [63:0] event_data_fifo [0:EVENT_FIFO_DEPTH-1];
    reg [31:0] event_param_fifo [0:EVENT_FIFO_DEPTH-1];
    reg [EVENT_FIFO_AWIDTH:0] event_fifo_wptr;
    reg [EVENT_FIFO_AWIDTH:0] event_fifo_rptr;
    reg [EVENT_FIFO_AWIDTH:0] event_fifo_wptr_next;
    reg [EVENT_FIFO_AWIDTH:0] event_fifo_rptr_next;

    // Event FIFO status
    wire event_fifo_empty;
    wire event_fifo_full;
    wire [EVENT_FIFO_AWIDTH:0] event_fifo_count;

    assign event_fifo_empty = (event_fifo_rptr == event_fifo_wptr);
    assign event_fifo_full = (event_fifo_wptr[EVENT_FIFO_AWIDTH] != event_fifo_rptr[EVENT_FIFO_AWIDTH]) &&
                            (event_fifo_wptr[EVENT_FIFO_AWIDTH-1:0] == event_fifo_rptr[EVENT_FIFO_AWIDTH-1:0]);

    function automatic [EVENT_FIFO_AWIDTH:0] calculate_event_fifo_count(
        input [EVENT_FIFO_AWIDTH:0] wptr,
        input [EVENT_FIFO_AWIDTH:0] rptr
    );
        if (wptr[EVENT_FIFO_AWIDTH] == rptr[EVENT_FIFO_AWIDTH]) begin
            calculate_event_fifo_count = wptr[EVENT_FIFO_AWIDTH-1:0] - rptr[EVENT_FIFO_AWIDTH-1:0];
        end else begin
            calculate_event_fifo_count = EVENT_FIFO_DEPTH - rptr[EVENT_FIFO_AWIDTH-1:0] + wptr[EVENT_FIFO_AWIDTH-1:0];
        end
    endfunction

    assign event_fifo_count = calculate_event_fifo_count(event_fifo_wptr, event_fifo_rptr);

    // Event interface outputs
    assign event_req_valid = !event_fifo_empty;
    assign event_type = event_req_valid ? event_type_fifo[event_fifo_rptr[EVENT_FIFO_AWIDTH-1:0]] : 32'h0;
    assign event_data = event_req_valid ? event_data_fifo[event_fifo_rptr[EVENT_FIFO_AWIDTH-1:0]] : 64'h0;
    assign event_param = event_req_valid ? event_param_fifo[event_fifo_rptr[EVENT_FIFO_AWIDTH-1:0]] : 32'h0;

    // Simple memory for demonstration
    reg [DATA_WIDTH-1:0] memory [0:1023];

    // Statistics and control
    int write_count = 0;
    int read_count = 0;
    int event_count = 0;
    reg test_completed = 0;

    // Polling counter
    reg [31:0] poll_counter = 0;

    // Instantiate the integrated SFIFO-HFIFO module
    mem_sfifo_bfm #(
        .INSTANCE_ID(INSTANCE_ID),
        .FIFO_DEPTH(FIFO_DEPTH),
        .ADDR_WIDTH(ADDR_WIDTH),
        .DATA_WIDTH(DATA_WIDTH),
        .DATA_WIDTH_IN_BYTE(DATA_WIDTH_IN_BYTE),
        .POLLING_INTERVAL(POLLING_INTERVAL),
    ) mem_sfifo_inst (
        .clk(clk),
        .rstn(rstn),

        // Unified request interface
        .req_valid(req_valid),
        .req_type(req_type),
        .req_addr(req_addr),
        .req_data(req_data),
        .req_mask(req_mask),
        .req_ready(req_ready),

        // Status
        .fifo_empty(fifo_empty),
        .fifo_full(fifo_full)
    );

    // Clock generation
    initial begin
        clk = 0;
        forever #5 clk = ~clk;  // 100MHz clock
    end

    // Reset and initialization sequence
    initial begin
        rstn = 0;
        req_ready = 1;
        event_req_ready = 1;

        // Initialize event FIFO pointers
        event_fifo_wptr = 0;
        event_fifo_rptr = 0;
        event_fifo_wptr_next = 0;
        event_fifo_rptr_next = 0;

        #100;
        rstn = 1;

        $display("[TB] Reset completed, starting enhanced SFIFO-HFIFO test");

        // Initialize enhanced BFM
        enhanced_init_bfm(INSTANCE_ID, 256*32, 64, 256, 8, 2);

        #200;

        // Start enhanced test
        $display("[TB] Starting enhanced test with Reference Model");
        enhanced_start_test();
    end

    reg [DATA_WIDTH-1:0] data_reg;
    reg read_valid;
    reg [ADDR_WIDTH-1:0] addr_reg;

    // Handle unified requests
    always @(posedge clk) begin
        if (!rstn) begin
            read_valid <= 0;
        end else begin
            if (req_type == 2'b00) begin
                read_valid <= req_valid;
                addr_reg <= req_addr;
            end else begin
                read_valid <= 0;
                addr_reg <= 0;
            end

            if (req_valid && req_ready) begin
                if (req_type == 2'b01) begin  // Write request
                    // Apply mask and write to memory
                    for (int i = 0; i < DATA_WIDTH_IN_BYTE; i++) begin
                        if (req_mask[i]) begin
                            memory[req_addr[9:0]][i*8 +: 8] <= req_data[i*8 +: 8];
                        end
                    end

                    write_count <= write_count + 1;
                    $display("[TB] Write processed: addr=0x%x, data=0x%x, count=%d",
                            req_addr, req_data, write_count + 1);
                end else if (req_type == 2'b00) begin  // Read request
                    // Read from memory
                    data_reg <= memory[req_addr[9:0]];
                    read_count <= read_count + 1;

                end
            end

            if (read_valid) begin
                $display("[TB] Read processed: addr=0x%x, data=0x%x, count=%d",
                        addr_reg, data_reg, read_count + 1);

                // Send read response back to software (with delay to simulate pipeline)
                h2s_read_data_resp(INSTANCE_ID, addr_reg, data_reg, DATA_WIDTH_IN_BYTE/4);
            end
        end
    end

    // Event polling logic
    always @(posedge clk) begin
        if (rstn) begin
            poll_counter <= poll_counter + 1;

            if (poll_counter >= POLLING_INTERVAL + 10) begin  // Slightly offset
                poll_counter <= 0;

                // Call import function to poll for events
                h2s_polling_event(INSTANCE_ID);
            end
        end
    end

    // Handle events from event FIFO
    always @(posedge clk) begin
        if (rstn && event_req_valid && event_req_ready) begin
            event_count <= event_count + 1;
            $display("[TB] Event processed: type=%d, data=0x%x, param=%d, count=%d",
                    event_type, event_data, event_param, event_count + 1);

            // Handle different event types
            case (event_type)
                0: begin  // TEST_COMPLETE
                    $display("[TB] ========================================");
                    $display("[TB] TEST COMPLETION EVENT RECEIVED!");
                    $display("[TB] ========================================");
                    test_completed <= 1;

                    // Wait a bit then finish
                    #1000;
                    $display("[TB] Final Statistics:");
                    $display("[TB]   Writes processed: %d", write_count);
                    $display("[TB]   Reads processed:  %d", read_count);
                    $display("[TB]   Events received:  %d", event_count);
                    $display("[TB] ========================================");
                    $display("[TB] ENHANCED TEST PASSED!");
                    $display("[TB] ========================================");
                    $finish;
                end

                1: begin  // ERROR_DETECTED
                    $display("[TB] ========================================");
                    $display("[TB] ERROR EVENT RECEIVED!");
                    $display("[TB] Error count: %d", event_data);
                    $display("[TB] Total reads: %d", event_param);
                    $display("[TB] ========================================");
                    test_completed <= 1;

                    #1000;
                    $display("[TB] ENHANCED TEST FAILED!");
                    $finish;
                end

                2: begin  // STATUS_UPDATE
                    $display("[TB] Status update: data=0x%x, param=%d", event_data, event_param);
                end

                default: begin
                    $display("[TB] Unknown event type: %d", event_type);
                end
            endcase
        end
    end

    // Event FIFO read logic
    always @(posedge clk) begin
        if (!rstn) begin
            event_fifo_rptr <= 0;
            event_fifo_wptr <= 0;
        end else begin
            event_fifo_rptr <= event_fifo_rptr_next;
            event_fifo_wptr <= event_fifo_wptr_next;
        end
    end

    always @(*) begin
        if (event_req_valid && event_req_ready) begin
            event_fifo_rptr_next <= event_fifo_rptr + 1;
        end else begin
            event_fifo_rptr_next <= event_fifo_rptr;
        end
    end

    // Status monitoring
    reg [31:0] status_counter = 0;
    always @(posedge clk) begin
        if (rstn && !test_completed) begin
            status_counter <= status_counter + 1;

            if (status_counter >= 10000) begin  // Every 100us
                automatic int fifo_size, test_complete, error_count;

                status_counter <= 0;
                enhanced_get_status(INSTANCE_ID, fifo_size, test_complete, error_count);

                if (fifo_size > 0 || test_complete > 0) begin
                    //$display("[TB] Status @ %t: FIFO=%d, Complete=%d, Errors=%d",
                    //        $time, fifo_size, test_complete, error_count);
                end
            end
        end
    end

    // Timeout protection
    initial begin
        #500000;  // 5ms timeout
        wait(test_completed);
        if (!test_completed) begin
            $display("[TB] ERROR: Test timeout! Enhanced test did not complete in time.");
            $display("[TB] Current status:");
            $display("[TB]   Writes processed: %d", write_count);
            $display("[TB]   Reads processed:  %d", read_count);
            $display("[TB]   Events received:  %d", event_count);
            $finish;
        end
    end

    // Export function for event response (called by software)
    function automatic void s2h_event_resp(
        input shortint unsigned instance_id,
        input int event_type,
        input longint unsigned event_data,
        input int unsigned event_param
    );
        // Write event to hardware FIFO
        if (!event_fifo_full) begin
            event_type_fifo[event_fifo_wptr[EVENT_FIFO_AWIDTH-1:0]] = event_type;
            event_data_fifo[event_fifo_wptr[EVENT_FIFO_AWIDTH-1:0]] = event_data;
            event_param_fifo[event_fifo_wptr[EVENT_FIFO_AWIDTH-1:0]] = event_param;
            event_fifo_wptr_next = event_fifo_wptr + 1;

            $display("[TB] Event added to FIFO: type=%d, data=0x%x, param=%d",
                    event_type, event_data, event_param);
        end else begin
            $display("[TB] ERROR: Event FIFO full, dropping event type=%d", event_type);
        end
    endfunction

    // DPI-C function imports
    import "DPI-C" context function void enhanced_init_bfm(
        input shortint unsigned id,
        input int unsigned page_size_in_bit,
        input longint unsigned addr_width,
        input shortint unsigned data_width,
        input int unsigned cont_line_per_channel,
        input byte unsigned merged_channel_num
    );

    import "DPI-C" context function void enhanced_start_test();

    import "DPI-C" context function void h2s_read_data_resp(
        input shortint unsigned instance_id,
        input longint unsigned addr,
        input bit [DATA_WIDTH-1:0] data,
        input int data_words
    );

    import "DPI-C" context function void h2s_polling_event(
        input shortint unsigned instance_id
    );

    import "DPI-C" context function void enhanced_get_status(
        input shortint unsigned id,
        output int fifo_size,
        output int test_complete,
        output int error_count
    );

    // Export the event response function
    export "DPI-C" function s2h_event_resp;

    // Optional: VCD dump for debugging
    `ifdef DUMP_VCD
    initial begin
        $dumpfile("mem_sfifo_enhanced_tb.vcd");
        $dumpvars(0, mem_sfifo_enhanced_tb);
        $display("[TB] VCD dumping enabled: mem_sfifo_enhanced_tb.vcd");
    end
    `endif

    `ifdef DUMP_FSDB
    initial begin
        $fsdbDumpfile("mem_sfifo_enhanced_tb.vcd");
        $fsdbDumpvars(0, mem_sfifo_enhanced_tb);
        $display("[TB] FSDB dumping enabled: mem_sfifo_enhanced_tb.fsdb");
    end
    `endif

endmodule

/**
 * This enhanced testbench demonstrates:
 *
 * 1. **Complete Verification Flow**:
 *    - Write data to RTL and Reference Model
 *    - Read data from RTL
 *    - Compare RTL data with Reference Model
 *    - Report PASS/FAIL for each transaction
 *
 * 2. **Event Communication**:
 *    - Software signals test completion to hardware
 *    - Hardware polls for events and handles them
 *    - Automatic test termination based on events
 *
 * 3. **Data Integrity Checking**:
 *    - Reference Model maintains expected data
 *    - Automatic comparison of read responses
 *    - Error counting and reporting
 *
 * 4. **Enhanced Monitoring**:
 *    - Real-time status reporting
 *    - Progress tracking
 *    - Timeout protection
 *
 * Key Improvements over Basic Version:
 * - Reference Model ensures data correctness
 * - Event system enables proper test completion
 * - Automatic verification reduces manual checking
 * - Complete end-to-end verification flow
 *
 * Usage:
 *   make enhanced_lib
 *   make enhanced_comp
 *   make enhanced_run
 */
