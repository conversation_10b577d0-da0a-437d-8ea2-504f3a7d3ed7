`timescale 1ns/1ps

/**
 * @brief Generic Software FIFO to Hardware FIFO (SFIFO-HFIFO) Hardware Module
 *
 * This module provides a reusable abstraction for the hardware side of SFIFO-HFIFO mechanism:
 * - Hardware side maintains a unified FIFO for both read and write requests from software
 * - Periodically polls software side for new requests
 * - Configurable FIFO depths and polling intervals
 */
module mem_sfifo_fifo #(
    parameter FIFO_DEPTH = 16,
    parameter ADDR_WIDTH = 64,
    parameter DATA_WIDTH = 256,
    parameter DATA_WIDTH_IN_BYTE = DATA_WIDTH/8,
    parameter POLLING_INTERVAL = 100,
    parameter REQ_TYPE_WIDTH = 2,  // Width of request type field
    parameter FIFO_AWIDTH = $clog2(FIFO_DEPTH)
)(
    input  wire clk,
    input  wire rstn,

    // FIFO status outputs
    output wire fifo_empty,
    output wire fifo_full,
    output wire [FIFO_AWIDTH:0] fifo_count,
    output wire [FIFO_AWIDTH:0] fifo_space,

    // Request output interface (to processing pipeline)
    output wire req_valid,
    output wire [REQ_TYPE_WIDTH-1:0] req_type,
    output wire [ADDR_WIDTH-1:0] req_addr,
    output wire [DATA_WIDTH-1:0] req_data,     // Only for write requests
    output wire [DATA_WIDTH_IN_BYTE-1:0] req_mask, // Only for write requests
    input  wire req_ready,

    // DPI-C polling interface
    output wire polling_trigger
);

    // Request type definitions
    localparam REQ_TYPE_READ = 2'b00;
    localparam REQ_TYPE_WRITE = 2'b01;

    // FIFO storage
    reg [REQ_TYPE_WIDTH-1:0] req_type_fifo [0:FIFO_DEPTH-1];
    reg [ADDR_WIDTH-1:0] req_addr_fifo [0:FIFO_DEPTH-1];
    reg [DATA_WIDTH-1:0] req_data_fifo [0:FIFO_DEPTH-1];
    reg [DATA_WIDTH_IN_BYTE-1:0] req_mask_fifo [0:FIFO_DEPTH-1];

    // FIFO pointers
    reg [FIFO_AWIDTH:0] fifo_wptr;
    reg [FIFO_AWIDTH:0] fifo_rptr;
    reg [FIFO_AWIDTH:0] fifo_wptr_next;
    reg [FIFO_AWIDTH:0] fifo_rptr_next;

    // Polling control
    reg [31:0] polling_counter;
    reg [31:0] polling_interval;

    // Status signals
    wire fifo_empty_int;
    wire fifo_full_int;
    wire [FIFO_AWIDTH:0] fifo_count_int;
    wire [FIFO_AWIDTH:0] fifo_space_int;

    // FIFO status calculation
    assign fifo_empty_int = (fifo_rptr == fifo_wptr);
    assign fifo_full_int = (fifo_wptr[FIFO_AWIDTH] != fifo_rptr[FIFO_AWIDTH]) &&
                          (fifo_wptr[FIFO_AWIDTH-1:0] == fifo_rptr[FIFO_AWIDTH-1:0]);

    function automatic [FIFO_AWIDTH:0] calculate_fifo_count(
        input [FIFO_AWIDTH:0] wptr,
        input [FIFO_AWIDTH:0] rptr
    );
        if (wptr[FIFO_AWIDTH] == rptr[FIFO_AWIDTH]) begin
            calculate_fifo_count = wptr[FIFO_AWIDTH-1:0] - rptr[FIFO_AWIDTH-1:0];
        end else begin
            calculate_fifo_count = FIFO_DEPTH - rptr[FIFO_AWIDTH-1:0] + wptr[FIFO_AWIDTH-1:0];
        end
    endfunction

    function automatic [FIFO_AWIDTH:0] calculate_fifo_space(
        input [FIFO_AWIDTH:0] wptr,
        input [FIFO_AWIDTH:0] rptr
    );
        calculate_fifo_space = FIFO_DEPTH - calculate_fifo_count(wptr, rptr);
    endfunction

    assign fifo_count_int = calculate_fifo_count(fifo_wptr, fifo_rptr);
    assign fifo_space_int = calculate_fifo_space(fifo_wptr, fifo_rptr);

    // Output assignments
    assign fifo_empty = fifo_empty_int;
    assign fifo_full = fifo_full_int;
    assign fifo_count = fifo_count_int;
    assign fifo_space = fifo_space_int;

    // Request output
    assign req_valid = !fifo_empty_int;
    assign req_type = req_type_fifo[fifo_rptr[FIFO_AWIDTH-1:0]];
    assign req_addr = req_addr_fifo[fifo_rptr[FIFO_AWIDTH-1:0]];
    assign req_data = req_data_fifo[fifo_rptr[FIFO_AWIDTH-1:0]];
    assign req_mask = req_mask_fifo[fifo_rptr[FIFO_AWIDTH-1:0]];

    // Polling trigger generation
    assign polling_trigger = (polling_counter == polling_interval) && !fifo_full_int;

    // Polling counter
    always @(posedge clk) begin
        if (!rstn) begin
            polling_counter <= 0;
            polling_interval <= POLLING_INTERVAL;
        end else begin
            if (polling_counter < polling_interval) begin
                polling_counter <= polling_counter + 1;
            end else begin
                polling_counter <= 0;
            end
        end
    end

    // FIFO pointer update logic
    always @(posedge clk) begin
        if (!rstn) begin
            fifo_wptr <= 0;
            fifo_rptr <= 0;
        end else begin
            fifo_wptr <= fifo_wptr_next;
            fifo_rptr <= fifo_rptr_next;
        end
    end

    // FIFO write interface functions (to be called by DPI-C)
    function automatic int push_request(
        input bit [REQ_TYPE_WIDTH-1:0] req_type,
        input bit [ADDR_WIDTH-1:0] addr,
        input bit [DATA_WIDTH-1:0] data,
        input bit [DATA_WIDTH_IN_BYTE-1:0] mask
    );
        if (!fifo_full_int) begin
            req_type_fifo[fifo_wptr[FIFO_AWIDTH-1:0]] = req_type;
            req_addr_fifo[fifo_wptr[FIFO_AWIDTH-1:0]] = addr;
            if (req_type == 2'b01) begin
                req_data_fifo[fifo_wptr[FIFO_AWIDTH-1:0]] = data;
                req_mask_fifo[fifo_wptr[FIFO_AWIDTH-1:0]] = mask;
            end else begin
                req_data_fifo[fifo_wptr[FIFO_AWIDTH-1:0]] = 0;
                req_mask_fifo[fifo_wptr[FIFO_AWIDTH-1:0]] = 0;
            end
            fifo_wptr_next = fifo_wptr + 1;
            $display("@%0t [mem_sfifo_fifo] Pushed request: type=%d, addr=0x%x, data=0x%x, mask=0x%x, wptr=%d",
                    $time, req_type, addr, data, mask, fifo_wptr);
            return 1;
        end else begin
            return 0;
        end
    endfunction

    // FIFO read logic
    always @(*) begin
        fifo_rptr_next = fifo_rptr;
        if (req_valid && req_ready) begin
            fifo_rptr_next = fifo_rptr + 1;
        end
    end

    // Initialize
    initial begin
        fifo_wptr = 0;
        fifo_rptr = 0;
        fifo_wptr_next = 0;
        fifo_rptr_next = 0;
        polling_counter = 0;
        polling_interval = POLLING_INTERVAL;
    end

endmodule

/**
 * @brief Integrated SFIFO-HFIFO module with DPI-C interface
 *
 * This module combines the generic SFIFO-HFIFO hardware with DPI-C interfaces
 * to provide a complete solution that can be easily integrated into existing designs.
 */
module mem_sfifo_bfm #(
    parameter INSTANCE_ID = 0,
    parameter FIFO_DEPTH = 16,
    parameter ADDR_WIDTH = 64,
    parameter DATA_WIDTH = 256,
    parameter DATA_WIDTH_IN_BYTE = DATA_WIDTH/8,
    parameter POLLING_INTERVAL = 100
)(
    input  wire clk,
    input  wire rstn,

    // Request output interface
    output wire req_valid,
    output wire [1:0] req_type,
    output wire [ADDR_WIDTH-1:0] req_addr,
    output wire [DATA_WIDTH-1:0] req_data,
    output wire [DATA_WIDTH_IN_BYTE-1:0] req_mask,
    input  wire req_ready,

    // Status outputs
    output wire fifo_empty,
    output wire fifo_full
);

    localparam FIFO_DEPTH_AWIDTH = $clog2(FIFO_DEPTH);

    // DPI-C function imports
    import "DPI-C" context function void mem_sfifo_init(
        input shortint unsigned instance_id
    );

    import "DPI-C" context function int mem_sfifo_poll_requests(
        input shortint unsigned instance_id,
        input int hw_available_space
    );

    // Export functions for software to push requests
    export "DPI-C" function h2s_mem_sfifo_push_request;

    // Unified FIFO instance
    wire polling_trigger;
    wire [FIFO_DEPTH_AWIDTH:0] fifo_space;

    mem_sfifo_fifo #(
        .FIFO_DEPTH(FIFO_DEPTH),
        .ADDR_WIDTH(ADDR_WIDTH),
        .DATA_WIDTH(DATA_WIDTH),
        .DATA_WIDTH_IN_BYTE(DATA_WIDTH_IN_BYTE),
        .POLLING_INTERVAL(POLLING_INTERVAL)
    ) unified_fifo_inst (
        .clk(clk),
        .rstn(rstn),
        .fifo_empty(fifo_empty),
        .fifo_full(fifo_full),
        .fifo_space(fifo_space),
        .req_valid(req_valid),
        .req_type(req_type),
        .req_addr(req_addr),
        .req_data(req_data),
        .req_mask(req_mask),
        .req_ready(req_ready),
        .polling_trigger(polling_trigger)
    );

    // Polling logic
    always @(posedge clk) begin
        if (rstn) begin
            if (polling_trigger) begin
                mem_sfifo_poll_requests(INSTANCE_ID, fifo_space);
            end
        end
    end

    // DPI-C functions for software to push requests
    function automatic int h2s_mem_sfifo_push_request(
        input bit [1:0] req_type,
        input bit [ADDR_WIDTH-1:0] addr,
        input bit [DATA_WIDTH-1:0] data,
        input bit [DATA_WIDTH_IN_BYTE-1:0] mask,
        input int data_words
    );
        return unified_fifo_inst.push_request(req_type, addr, data, mask);
    endfunction

    // Initialize
    initial begin
        mem_sfifo_init(INSTANCE_ID);
    end

endmodule
