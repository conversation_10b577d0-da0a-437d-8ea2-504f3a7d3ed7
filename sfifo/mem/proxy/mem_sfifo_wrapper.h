#ifndef MEM_SFIFO_WRAPPER_H
#define MEM_SFIFO_WRAPPER_H

#include "mem_sfifo_client.h"
#include "svdpi.h"
#include "vpi_user.h"
#include <memory>
#include <unordered_map>

/**
 * @brief Integrated Memory SFIFO wrapper that combines software and hardware sides
 * 
 * This class provides a complete abstraction for the SFIFO mechanism,
 * managing both software FIFOs and DPI-C interfaces to hardware.
 */
class MemSfifoWrapper {
public:
    /**
     * @brief Constructor
     * @param scope_name SystemVerilog scope name for DPI-C calls
     */
    MemSfifoWrapper(const std::string& scope_name);
    
    /**
     * @brief Destructor
     */
    ~MemSfifoWrapper();
    
    /**
     * @brief Add a write request to the software FIFO
     * @param addr Address to write
     * @param data Data to write
     * @param mask Write mask
     */
    void addWriteRequest(uint64_t addr, const std::vector<uint32_t>& data, 
                        const std::vector<uint32_t>& mask);
    
    /**
     * @brief Add a read request to the software FIFO
     * @param addr Address to read
     */
    void addReadRequest(uint64_t addr);
    
    /**
     * @brief Get current FIFO size
     * @return Number of pending requests
     */
    size_t getFifoSize() const;
    
    /**
     * @brief Clear all pending requests
     */
    void clearAllRequests();
    
    // DPI-C callback functions (called by hardware)
    /**
     * @brief Hardware polling function for requests
     * @param hw_available_space Number of requests hardware can accept
     * @return Number of requests sent to hardware
     */
    int pollRequests(int hw_available_space);

private:
    std::string scope_name_;
    
    // Software FIFO client
    std::unique_ptr<UnifiedMemClient> client_;
    
    // DPI-C interface functions
    void setupDpiInterface();
    
    // Callback function for sending a single request to hardware
    int sendRequest(const Request& request);
};

/**
 * @brief Global manager for multiple Memory SFIFO instances
 * 
 * This singleton manages multiple SFIFO instances, typically one per
 * SystemVerilog module instance.
 */
class MemSfifoManager {
public:
    /**
     * @brief Get singleton instance
     * @return Reference to the singleton instance
     */
    static MemSfifoManager& getInstance();
    
    /**
     * @brief Register a new SFIFO instance
     * @param instance_id Unique identifier for the instance
     * @param scope_name SystemVerilog scope name
     * @return Pointer to the created wrapper instance
     */
    MemSfifoWrapper* registerInstance(uint16_t instance_id,
                                       const std::string& scope_name);
    
    /**
     * @brief Get an existing instance
     * @param instance_id Instance identifier
     * @return Pointer to the wrapper instance, or nullptr if not found
     */
    MemSfifoWrapper* getInstance(uint16_t instance_id);
    
    /**
     * @brief Get instance by SystemVerilog scope
     * @param scope SystemVerilog scope
     * @return Pointer to the wrapper instance, or nullptr if not found
     */
    MemSfifoWrapper* getInstanceByScope(svScope scope);
    
    /**
     * @brief Unregister an instance
     * @param instance_id Instance identifier
     */
    void unregisterInstance(uint16_t instance_id);
    
    /**
     * @brief Clear all instances
     */
    void clearAllInstances();

private:
    MemSfifoManager() = default;
    ~MemSfifoManager() = default;
    
    // Prevent copying
    MemSfifoManager(const MemSfifoManager&) = delete;
    MemSfifoManager& operator=(const MemSfifoManager&) = delete;
    
    std::unordered_map<uint16_t, std::unique_ptr<MemSfifoWrapper>> instances_;
    std::unordered_map<svScope, uint16_t> scope_to_id_map_;
};

// Global DPI-C functions that can be called from SystemVerilog
extern "C" {
    /**
     * @brief Initialize a new SFIFO instance
     * @param instance_id Unique identifier for the instance
     */
    void mem_sfifo_init(uint16_t instance_id);
    
    /**
     * @brief Hardware polling function for requests
     * @param instance_id Instance identifier
     * @param hw_available_space Number of requests hardware can accept
     * @return Number of requests sent to hardware
     */
    int mem_sfifo_poll_requests(uint16_t instance_id, int hw_available_space);
    
    /**
     * @brief Add a write request (called from software/testbench)
     * @param instance_id Instance identifier
     * @param addr Address to write
     * @param data Data to write (array of 32-bit words)
     * @param data_words Number of 32-bit words in data
     * @param mask Write mask (array of 32-bit words)
     * @param mask_words Number of 32-bit words in mask
     */
    void mem_sfifo_add_write(uint16_t instance_id, uint64_t addr,
                              const uint32_t* data, int data_words,
                              const uint32_t* mask, int mask_words);
    
    /**
     * @brief Add a read request (called from software/testbench)
     * @param instance_id Instance identifier
     * @param addr Address to read
     */
    void mem_sfifo_add_read(uint16_t instance_id, uint64_t addr);
    
    /**
     * @brief Get FIFO size
     * @param instance_id Instance identifier
     * @return Number of pending requests
     */
    int mem_sfifo_get_size(uint16_t instance_id);
}

#endif // MEM_SFIFO_WRAPPER_H
