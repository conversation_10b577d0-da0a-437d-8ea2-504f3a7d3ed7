#ifndef MEM_SFIFO_CLIENT_H
#define MEM_SFIFO_CLIENT_H

#include <queue>
#include <mutex>
#include <vector>
#include <functional>
#include <stdint.h>
#include "svdpi.h"
#include "vpi_user.h"

/**
 * @brief Generic Memory Software FIFO (SFIFO) Client
 * 
 * This class provides a reusable abstraction for the SFIFO mechanism where:
 * - Software side stores requests in local FIFOs
 * - Hardware side polls the software side for requests
 * - Supports batch operations for better performance
 * 
 * @tparam RequestType The type of request data structure
 */
template<typename RequestType>
class MemSfifoClient {
public:
    /**
     * @brief Callback function type for sending requests to hardware
     * @param requests Vector of requests to send
     * @return 1 if request was sent successfully, 0 otherwise
     */
    using SendCallback = std::function<int(const RequestType&)>;

    /**
     * @brief Constructor
     * @param send_callback Function to call when hardware polls for requests
     */
    MemSfifoClient(SendCallback send_callback)
        : send_callback_(send_callback) {}

    /**
     * @brief Add a request to the software FIFO
     * @param request The request to add
     */
    void pushRequest(const RequestType& request) {
        std::lock_guard<std::mutex> lock(fifo_mutex_);
        request_fifo_.push(request);
        vpi_printf("[sfifo_hfifo_client] pushRequest: request_fifo_.size()=%d\n", request_fifo_.size());
    }

    /**
     * @brief Hardware polling function - called by hardware to get requests
     * @param hw_available_space Number of requests hardware can accept
     * @return Number of requests sent to hardware
     */
    int pollRequests(int hw_available_space) {
        std::lock_guard<std::mutex> lock(fifo_mutex_);

        if (request_fifo_.empty()) {
            return 0;
        }

        vpi_printf("[sfifo_hfifo_client] pollRequests: hw_available_space=%d, request_fifo_.size()=%d\n",
                   hw_available_space, request_fifo_.size());

        if (hw_available_space) {
            const RequestType& request = request_fifo_.front();

            // Send one request
            send_callback_(request); 
            request_fifo_.pop(); // Only pop if successfully sent
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * @brief Get current FIFO size
     * @return Number of pending requests
     */
    size_t size() const {
        std::lock_guard<std::mutex> lock(fifo_mutex_);
        return request_fifo_.size();
    }

    /**
     * @brief Check if FIFO is empty
     * @return true if empty, false otherwise
     */
    bool empty() const {
        std::lock_guard<std::mutex> lock(fifo_mutex_);
        return request_fifo_.empty();
    }

    /**
     * @brief Clear all pending requests
     */
    void clear() {
        std::lock_guard<std::mutex> lock(fifo_mutex_);
        std::queue<RequestType> empty_queue;
        request_fifo_.swap(empty_queue);
    }

private:
    std::queue<RequestType> request_fifo_;
    mutable std::mutex fifo_mutex_;
    SendCallback send_callback_;
};

/**
 * @brief Request type definitions
 */
enum class RequestType : uint8_t {
    READ = 0,
    WRITE = 1
};

/**
 * @brief Unified request data structure
 */
struct Request {
    RequestType type;
    uint64_t addr;
    std::vector<uint32_t> data;  // Only used for write requests
    std::vector<uint32_t> mask;  // Only used for write requests
    
    Request() = default;
    
    // Constructor for read requests
    Request(uint64_t a) : type(RequestType::READ), addr(a) {}
    
    // Constructor for write requests
    Request(uint64_t a, const std::vector<uint32_t>& d, const std::vector<uint32_t>& m)
        : type(RequestType::WRITE), addr(a), data(d), mask(m) {}
};

/**
 * @brief Specialized Memory SFIFO client for unified request handling
 */
using UnifiedMemClient = MemSfifoClient<Request>;

#endif // MEM_SFIFO_CLIENT_H
