#include "mem_sfifo_wrapper.h"
#include <algorithm>

// Forward declarations for DPI-C functions exported from SystemVerilog
extern "C" {
    extern int h2s_mem_sfifo_push_request(svBitVecVal* req_type, svBitVecVal* addr, const svBitVecVal* data,
                              const svBitVecVal* mask, int data_words);
}

// Forward declarations - actual implementations will be provided by the specific DPI example

// MemSfifoWrapper Implementation
MemSfifoWrapper::MemSfifoWrapper(const std::string& scope_name)
    : scope_name_(scope_name) {

    // Create unified client with callback
    auto callback = [this](const Request& request) -> int {
        return this->sendRequest(request);
    };
    client_ = std::make_unique<UnifiedMemClient>(callback);

    setupDpiInterface();
}

MemSfifoWrapper::~MemSfifoWrapper() {
    // Cleanup if needed
}

void MemSfifoWrapper::addWriteRequest(uint64_t addr, const std::vector<uint32_t>& data,
                                       const std::vector<uint32_t>& mask) {
    vpi_printf("[MemSfifoWrapper] addWriteRequest: addr=0x%llx, data.size()=%d, mask.size()=%d\n",
               addr, data.size(), mask.size());
    Request req(addr, data, mask);
    client_->pushRequest(req);
}

void MemSfifoWrapper::addReadRequest(uint64_t addr) {
    Request req(addr);
    client_->pushRequest(req);
}

size_t MemSfifoWrapper::getFifoSize() const {
    return client_->size();
}

void MemSfifoWrapper::clearAllRequests() {
    client_->clear();
}

int MemSfifoWrapper::pollRequests(int hw_available_space) {
    return client_->pollRequests(hw_available_space);
}

void MemSfifoWrapper::setupDpiInterface() {
    // Set up SystemVerilog scope for DPI-C calls
    svScope scope = svGetScopeFromName(scope_name_.c_str());
    if (scope) {
        svSetScope(scope);
    } else {
        vpi_printf("Warning: Could not find scope %s\n", scope_name_.c_str());
    }
}

int MemSfifoWrapper::sendRequest(const Request& req) {
    //vpi_printf("[MemSfifoWrapper] scope: %s\n", svGetNameFromScope(svGetScope()));

    RequestType req_type = req.type;
    uint64_t req_addr = req.addr;
    int data_words = 0;
    svBitVecVal* data = reinterpret_cast<svBitVecVal*>(&data_words);
    svBitVecVal* mask = reinterpret_cast<svBitVecVal*>(&data_words);

    if (req.type == RequestType::WRITE) {
        data = reinterpret_cast<svBitVecVal*>(const_cast<uint32_t*>(req.data.data()));
        mask = reinterpret_cast<svBitVecVal*>(const_cast<uint32_t*>(req.mask.data()));
        data_words = static_cast<int>(req.data.size());
    }

    vpi_printf("[MemSfifoWrapper] sendRequest: req_type=%d, req_addr=0x%llx, data_words=%d\n",
               static_cast<int>(req.type), req.addr, data_words);

    return h2s_mem_sfifo_push_request(reinterpret_cast<svBitVecVal*>(&req_type),
                                      reinterpret_cast<svBitVecVal*>(&req_addr),
                                      data, mask, data_words);
}

// MemSfifoManager Implementation
MemSfifoManager& MemSfifoManager::getInstance() {
    static MemSfifoManager instance;
    return instance;
}

MemSfifoWrapper* MemSfifoManager::registerInstance(uint16_t instance_id,
                                                      const std::string& scope_name) {
    auto wrapper = std::make_unique<MemSfifoWrapper>(scope_name);
    MemSfifoWrapper* wrapper_ptr = wrapper.get();

    instances_[instance_id] = std::move(wrapper);

    // Map scope to instance ID
    svScope scope = svGetScopeFromName(scope_name.c_str());
    if (scope) {
        scope_to_id_map_[scope] = instance_id;
    }

    return wrapper_ptr;
}

MemSfifoWrapper* MemSfifoManager::getInstance(uint16_t instance_id) {
    auto it = instances_.find(instance_id);
    return (it != instances_.end()) ? it->second.get() : nullptr;
}

MemSfifoWrapper* MemSfifoManager::getInstanceByScope(svScope scope) {
    auto it = scope_to_id_map_.find(scope);
    if (it != scope_to_id_map_.end()) {
        return getInstance(it->second);
    }
    return nullptr;
}

void MemSfifoManager::unregisterInstance(uint16_t instance_id) {
    auto it = instances_.find(instance_id);
    if (it != instances_.end()) {
        // Remove from scope map
        for (auto scope_it = scope_to_id_map_.begin(); scope_it != scope_to_id_map_.end(); ++scope_it) {
            if (scope_it->second == instance_id) {
                scope_to_id_map_.erase(scope_it);
                break;
            }
        }
        instances_.erase(it);
    }
}

void MemSfifoManager::clearAllInstances() {
    instances_.clear();
    scope_to_id_map_.clear();
}

// Global DPI-C functions implementation
extern "C" {
    void mem_sfifo_init(uint16_t instance_id) {
        svScope scope = svGetScope();
        if (!scope) {
            vpi_printf("Error: Could not get scope in mem_sfifo_init\n");
            return;
        }

        std::string scope_name = svGetNameFromScope(scope);
        MemSfifoManager::getInstance().registerInstance(instance_id, scope_name);

        vpi_printf("[SFIFO-HFIFO] Initialized instance %d with scope %s\n",
                   instance_id, scope_name.c_str());
    }

    int mem_sfifo_poll_requests(uint16_t instance_id, int hw_available_space) {
        MemSfifoWrapper* wrapper = MemSfifoManager::getInstance().getInstance(instance_id);
        if (!wrapper) {
            return 0;
        }
        return wrapper->pollRequests(hw_available_space);
    }

    void mem_sfifo_add_write(uint16_t instance_id, uint64_t addr,
                              const uint32_t* data, int data_words,
                              const uint32_t* mask, int mask_words) {
        MemSfifoWrapper* wrapper = MemSfifoManager::getInstance().getInstance(instance_id);
        if (!wrapper) {
            return;
        }

        std::vector<uint32_t> data_vec(data, data + data_words);
        std::vector<uint32_t> mask_vec(mask, mask + mask_words);
        wrapper->addWriteRequest(addr, data_vec, mask_vec);
    }

    void mem_sfifo_add_read(uint16_t instance_id, uint64_t addr) {
        MemSfifoWrapper* wrapper = MemSfifoManager::getInstance().getInstance(instance_id);
        if (!wrapper) {
            return;
        }
        wrapper->addReadRequest(addr);
    }

    int mem_sfifo_get_size(uint16_t instance_id) {
        MemSfifoWrapper* wrapper = MemSfifoManager::getInstance().getInstance(instance_id);
        if (!wrapper) {
            return 0;
        }
        return static_cast<int>(wrapper->getFifoSize());
    }
}
