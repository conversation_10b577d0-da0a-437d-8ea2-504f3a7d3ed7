#ifndef EVENT_SHFIFO_WRAPPER_H
#define EVENT_SHFIFO_WRAPPER_H

#include "event_sfifo_client.h"
#include "svdpi.h"
#include "vpi_user.h"
#include <memory>
#include <unordered_map>

/**
 * @brief Integrated Event-SHFIFO wrapper that combines software and hardware sides
 * 
 * This class provides a complete abstraction for the Event-SHFIFO mechanism,
 * managing both software FIFOs and DPI-C interfaces to hardware.
 */
class EventSfifoWrapper {
public:
    /**
     * @brief Constructor
     * @param scope_name SystemVerilog scope name for DPI-C calls
     * @param max_batch_size Maximum batch size for transfers
     */
    EventSfifoWrapper(const std::string& scope_name, int max_batch_size = 4);
    
    /**
     * @brief Destructor
     */
    ~EventSfifoWrapper();
    
    /**
     * @brief Add an event to the software FIFO
     * @param type Event type
     * @param data Event data
     * @param param Event parameter
     */
    void addEvent(uint32_t type, uint64_t data, uint32_t param);
    
    /**
     * @brief Get current FIFO size
     * @return Number of pending events
     */
    size_t getFifoSize() const;
    
    /**
     * @brief Clear all pending events
     */
    void clearAllEvents();
    
    // DPI-C callback functions (called by hardware)
    /**
     * @brief Hardware polling function for events
     * @param hw_available_space Number of events hardware can accept
     * @return Number of events sent to hardware
     */
    int pollEvents(int hw_available_space);

private:
    std::string scope_name_;
    int max_batch_size_;
    
    // Software FIFO client
    std::unique_ptr<EventSfifoClient> client_;
    
    // DPI-C interface functions
    void setupDpiInterface();
    
    // Callback function for sending events to hardware
    int sendEvents(const std::vector<EventSfifoClient::Event>& events, int max_count);
};

/**
 * @brief Global manager for multiple Event-SHFIFO instances
 * 
 * This singleton manages multiple Event-SHFIFO instances, typically one per
 * SystemVerilog module instance.
 */
class EventSfifoManager {
public:
    /**
     * @brief Get singleton instance
     * @return Reference to the singleton instance
     */
    static EventSfifoManager& getInstance();
    
    /**
     * @brief Register a new Event-SHFIFO instance
     * @param instance_id Unique identifier for the instance
     * @param scope_name SystemVerilog scope name
     * @param max_batch_size Maximum batch size for transfers
     * @return Pointer to the created wrapper instance
     */
    EventSfifoWrapper* registerInstance(uint16_t instance_id, 
                                       const std::string& scope_name,
                                       int max_batch_size = 4);
    
    /**
     * @brief Get an existing instance
     * @param instance_id Instance identifier
     * @return Pointer to the wrapper instance, or nullptr if not found
     */
    EventSfifoWrapper* getInstance(uint16_t instance_id);
    
    /**
     * @brief Get instance by SystemVerilog scope
     * @param scope SystemVerilog scope
     * @return Pointer to the wrapper instance, or nullptr if not found
     */
    EventSfifoWrapper* getInstanceByScope(svScope scope);
    
    /**
     * @brief Unregister an instance
     * @param instance_id Instance identifier
     */
    void unregisterInstance(uint16_t instance_id);
    
    /**
     * @brief Clear all instances
     */
    void clearAllInstances();

private:
    EventSfifoManager() = default;
    ~EventSfifoManager() = default;
    
    // Prevent copying
    EventSfifoManager(const EventSfifoManager&) = delete;
    EventSfifoManager& operator=(const EventSfifoManager&) = delete;
    
    std::unordered_map<uint16_t, std::unique_ptr<EventSfifoWrapper>> instances_;
    std::unordered_map<svScope, uint16_t> scope_to_id_map_;
};

// Global DPI-C functions that can be called from SystemVerilog
extern "C" {
    /**
     * @brief Initialize a new Event-SHFIFO instance
     * @param instance_id Unique identifier for the instance
     * @param max_batch_size Maximum batch size for transfers
     */
    void event_sfifo_init(uint16_t instance_id, int max_batch_size);
    
    /**
     * @brief Hardware polling function for events
     * @param instance_id Instance identifier
     * @param hw_available_space Number of events hardware can accept
     * @return Number of events sent to hardware
     */
    int event_sfifo_poll_events(uint16_t instance_id, int hw_available_space);
    
    /**
     * @brief Add an event (called from software/testbench)
     * @param instance_id Instance identifier
     * @param type Event type
     * @param data Event data
     * @param param Event parameter
     */
    void event_sfifo_add_event(uint16_t instance_id, uint32_t type,
                               uint64_t data, uint32_t param);
    
    /**
     * @brief Get FIFO size
     * @param instance_id Instance identifier
     * @return Number of pending events
     */
    int event_sfifo_get_size(uint16_t instance_id);
}

#endif // EVENT_SHFIFO_WRAPPER_H 