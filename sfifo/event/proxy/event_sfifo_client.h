#ifndef EVENT_SHFIFO_CLIENT_H
#define EVENT_SHFIFO_CLIENT_H

#include <queue>
#include <mutex>
#include <vector>
#include <functional>
#include <stdint.h>
#include "svdpi.h"
#include "vpi_user.h"

/**
 * @brief Event Software FIFO to Hardware FIFO (Event-SHFIFO) Client
 * 
 * This class provides a reusable abstraction for the Event-SHFIFO mechanism where:
 * - Software side stores events in local FIFOs
 * - Hardware side polls the software side for events
 * - Supports batch operations for better performance
 */
class EventSfifoClient {
public:
    /**
     * @brief Event data structure
     */
    struct Event {
        uint32_t type;
        uint64_t data;
        uint32_t param;

        Event(uint32_t t = 0, uint64_t d = 0, uint32_t p = 0) 
            : type(t), data(d), param(p) {}
    };

    /**
     * @brief Callback function type for sending events to hardware
     * @param events Vector of events to send
     * @param max_count Maximum number of events hardware can accept
     * @return Number of events actually sent
     */
    using SendCallback = std::function<int(const std::vector<Event>&, int)>;

    /**
     * @brief Constructor
     * @param send_callback Function to call when hardware polls for events
     * @param max_batch_size Maximum number of events to send in one batch
     */
    EventSfifoClient(SendCallback send_callback, int max_batch_size = 1)
        : send_callback_(send_callback), max_batch_size_(max_batch_size) {}

    /**
     * @brief Add an event to the software FIFO
     * @param event The event to add
     */
    void pushEvent(const Event& event) {
        std::lock_guard<std::mutex> lock(fifo_mutex_);
        event_fifo_.push(event);
        vpi_printf("[event_shfifo_client] pushEvent: event_fifo_.size()=%d\n", event_fifo_.size());
    }

    /**
     * @brief Hardware polling function - called by hardware to get events
     * @param hw_available_space Number of events hardware can accept
     * @return Number of events sent to hardware
     */
    int pollEvents(int hw_available_space) {
        std::lock_guard<std::mutex> lock(fifo_mutex_);

        if (event_fifo_.empty()) {
            return 0;
        }

        vpi_printf("[event_shfifo_client] pollEvents: hw_available_space=%d, event_fifo_.size()=%d\n",
                   hw_available_space, event_fifo_.size());

        // Calculate how many events to send (optimize for batch)
        int batch_size = std::min({
            static_cast<int>(event_fifo_.size()),
            hw_available_space,
            max_batch_size_
        });

        if (batch_size == 0) {
            return 0;
        }

        // Prepare batch of events
        std::vector<Event> batch;
        batch.reserve(batch_size);
        
        for (int i = 0; i < batch_size; ++i) {
            batch.push_back(event_fifo_.front());
            event_fifo_.pop();
        }

        // Send to hardware
        return send_callback_(batch, hw_available_space);
    }

    /**
     * @brief Get current FIFO size
     * @return Number of pending events
     */
    size_t size() const {
        std::lock_guard<std::mutex> lock(fifo_mutex_);
        return event_fifo_.size();
    }

    /**
     * @brief Check if FIFO is empty
     * @return true if empty, false otherwise
     */
    bool empty() const {
        std::lock_guard<std::mutex> lock(fifo_mutex_);
        return event_fifo_.empty();
    }

    /**
     * @brief Clear all pending events
     */
    void clear() {
        std::lock_guard<std::mutex> lock(fifo_mutex_);
        std::queue<Event> empty_queue;
        event_fifo_.swap(empty_queue);
    }

private:
    std::queue<Event> event_fifo_;
    mutable std::mutex fifo_mutex_;
    SendCallback send_callback_;
    int max_batch_size_;
};

#endif // EVENT_SHFIFO_CLIENT_H 