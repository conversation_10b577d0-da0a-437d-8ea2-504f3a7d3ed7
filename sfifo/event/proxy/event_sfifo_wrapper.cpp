#include "event_sfifo_wrapper.h"
#include <algorithm>

// Forward declarations for DPI-C functions exported from SystemVerilog
extern "C" {
    extern int h2s_event_sfifo_push_event(svBitVecVal* type, svBitVecVal* data, svBitVecVal* param);
    extern int h2s_event_sfifo_push_event_batch(const uint32_t* type_array, const uint64_t* data_array,
                                 const uint32_t* param_array, int batch_size);
}

// EventShfifoWrapper Implementation
EventSfifoWrapper::EventSfifoWrapper(const std::string& scope_name, int max_batch_size)
    : scope_name_(scope_name), max_batch_size_(max_batch_size) {

    // Create client with callback
    auto callback = [this](const std::vector<EventSfifoClient::Event>& events, int max_count) -> int {
        return this->sendEvents(events, max_count);
    };
    client_ = std::make_unique<EventSfifoClient>(callback, max_batch_size);

    setupDpiInterface();
}

EventSfifoWrapper::~EventSfifoWrapper() {
    // Cleanup if needed
}

void EventSfifoWrapper::addEvent(uint32_t type, uint64_t data, uint32_t param) {
    vpi_printf("[EventSfifoWrapper] addEvent: type=%d, data=0x%llx, param=%d\n",
               type, data, param);
    EventSfifoClient::Event event(type, data, param);
    client_->pushEvent(event);
}

size_t EventSfifoWrapper::getFifoSize() const {
    return client_->size();
}

void EventSfifoWrapper::clearAllEvents() {
    client_->clear();
}

int EventSfifoWrapper::pollEvents(int hw_available_space) {
    return client_->pollEvents(hw_available_space);
}

void EventSfifoWrapper::setupDpiInterface() {
    // Set up SystemVerilog scope for DPI-C calls
    svScope scope = svGetScopeFromName(scope_name_.c_str());
    if (scope) {
        svSetScope(scope);
    } else {
        vpi_printf("Warning: Could not find scope %s\n", scope_name_.c_str());
    }
}

int EventSfifoWrapper::sendEvents(const std::vector<EventSfifoClient::Event>& events, int max_count) {
    if (events.empty()) {
        return 0;
    }

    vpi_printf("[EventSfifoWrapper] scope: %s\n", svGetNameFromScope(svGetScope()));

    int sent_count = 0;

    if (events.size() == 1) {
        // Single event - use simple interface
        const EventSfifoClient::Event& event = events[0];
        uint32_t type = event.type;
        uint64_t data = event.data;
        uint32_t param = event.param;

        if (h2s_event_sfifo_push_event(reinterpret_cast<svBitVecVal*>(&type),
                         reinterpret_cast<svBitVecVal*>(&data),
                         reinterpret_cast<svBitVecVal*>(&param))) {
            sent_count = 1;
        }
    } else {
        // Batch events - use batch interface for better performance
        int batch_size = std::min(static_cast<int>(events.size()), max_count);

        // Prepare batch arrays
        std::vector<uint32_t> type_array(batch_size);
        std::vector<uint64_t> data_array(batch_size);
        std::vector<uint32_t> param_array(batch_size);

        for (int i = 0; i < batch_size; ++i) {
            type_array[i] = events[i].type;
            data_array[i] = events[i].data;
            param_array[i] = events[i].param;
        }

        sent_count = h2s_event_sfifo_push_event_batch(type_array.data(), data_array.data(),
                                       param_array.data(), batch_size);
    }

    return sent_count;
}

// EventShfifoManager Implementation
EventSfifoManager& EventSfifoManager::getInstance() {
    static EventSfifoManager instance;
    return instance;
}

EventSfifoWrapper* EventSfifoManager::registerInstance(uint16_t instance_id,
                                                       const std::string& scope_name,
                                                       int max_batch_size) {
    auto wrapper = std::make_unique<EventSfifoWrapper>(scope_name, max_batch_size);
    EventSfifoWrapper* wrapper_ptr = wrapper.get();

    instances_[instance_id] = std::move(wrapper);

    // Map scope to instance ID
    svScope scope = svGetScopeFromName(scope_name.c_str());
    if (scope) {
        scope_to_id_map_[scope] = instance_id;
    }

    return wrapper_ptr;
}

EventSfifoWrapper* EventSfifoManager::getInstance(uint16_t instance_id) {
    auto it = instances_.find(instance_id);
    return (it != instances_.end()) ? it->second.get() : nullptr;
}

EventSfifoWrapper* EventSfifoManager::getInstanceByScope(svScope scope) {
    auto it = scope_to_id_map_.find(scope);
    if (it != scope_to_id_map_.end()) {
        return getInstance(it->second);
    }
    return nullptr;
}

void EventSfifoManager::unregisterInstance(uint16_t instance_id) {
    auto it = instances_.find(instance_id);
    if (it != instances_.end()) {
        // Remove from scope map
        for (auto scope_it = scope_to_id_map_.begin(); scope_it != scope_to_id_map_.end(); ++scope_it) {
            if (scope_it->second == instance_id) {
                scope_to_id_map_.erase(scope_it);
                break;
            }
        }
        instances_.erase(it);
    }
}

void EventSfifoManager::clearAllInstances() {
    instances_.clear();
    scope_to_id_map_.clear();
}

// Global DPI-C functions implementation
extern "C" {
    void event_sfifo_init(uint16_t instance_id, int max_batch_size) {
        svScope scope = svGetScope();
        if (!scope) {
            vpi_printf("Error: Could not get scope in event_sfifo_init\n");
            return;
        }

        std::string scope_name = svGetNameFromScope(scope);
        EventSfifoManager::getInstance().registerInstance(instance_id, scope_name, max_batch_size);

        vpi_printf("[Event-SFIFO] Initialized instance %d with scope %s, max_batch_size=%d\n",
                   instance_id, scope_name.c_str(), max_batch_size);
    }

    int event_sfifo_poll_events(uint16_t instance_id, int hw_available_space) {
        EventSfifoWrapper* wrapper = EventSfifoManager::getInstance().getInstance(instance_id);
        if (!wrapper) {
            return 0;
        }
        return wrapper->pollEvents(hw_available_space);
    }

    void event_sfifo_add_event(uint16_t instance_id, uint32_t type,
                               uint64_t data, uint32_t param) {
        EventSfifoWrapper* wrapper = EventSfifoManager::getInstance().getInstance(instance_id);
        if (!wrapper) {
            return;
        }
        wrapper->addEvent(type, data, param);
    }

    int event_sfifo_get_size(uint16_t instance_id) {
        EventSfifoWrapper* wrapper = EventSfifoManager::getInstance().getInstance(instance_id);
        if (!wrapper) {
            return 0;
        }
        return static_cast<int>(wrapper->getFifoSize());
    }
} 