`timescale 1ns/1ps

/**
 * @brief Event Software FIFO to Hardware FIFO (Event-SHFIFO) Hardware Module
 *
 * This module provides a reusable abstraction for the hardware side of Event-SHFIFO mechanism:
 * - Hardware side maintains a FIFO for event data from software
 * - Periodically polls software side for new events
 * - Supports batch operations for better performance
 * - Configurable FIFO depths and polling intervals
 */
module event_sfifo_fifo #(
    parameter FIFO_DEPTH = 8,
    parameter EVENT_TYPE_WIDTH = 32,
    parameter EVENT_DATA_WIDTH = 64,
    parameter EVENT_PARAM_WIDTH = 32,
    parameter POLLING_INTERVAL = 100,
    parameter MAX_BATCH_SIZE = 4,
    parameter FIFO_AWIDTH = $clog2(FIFO_DEPTH)
)(
    input  wire clk,
    input  wire rstn,

    // FIFO status outputs
    output wire fifo_empty,
    output wire fifo_full,
    output wire [FIFO_AWIDTH:0] fifo_count,
    output wire [FIFO_AWIDTH:0] fifo_space,

    // Event output interface (to processing pipeline)
    output wire event_valid,
    output wire [EVENT_TYPE_WIDTH-1:0] event_type,
    output wire [EVENT_DATA_WIDTH-1:0] event_data,
    output wire [EVENT_PARAM_WIDTH-1:0] event_param,
    input  wire event_ready,

    // DPI-C polling interface
    output wire polling_trigger
);

    // FIFO storage
    reg [EVENT_TYPE_WIDTH-1:0] event_type_fifo [0:FIFO_DEPTH-1];
    reg [EVENT_DATA_WIDTH-1:0] event_data_fifo [0:FIFO_DEPTH-1];
    reg [EVENT_PARAM_WIDTH-1:0] event_param_fifo [0:FIFO_DEPTH-1];

    // FIFO pointers
    reg [FIFO_AWIDTH:0] fifo_wptr;
    reg [FIFO_AWIDTH:0] fifo_rptr;
    reg [FIFO_AWIDTH:0] fifo_wptr_next;
    reg [FIFO_AWIDTH:0] fifo_rptr_next;

    // Polling control
    reg [31:0] polling_counter;
    reg [31:0] polling_interval;

    // Status signals
    wire fifo_empty_int;
    wire fifo_full_int;
    wire [FIFO_AWIDTH:0] fifo_count_int;
    wire [FIFO_AWIDTH:0] fifo_space_int;

    // FIFO status calculation
    assign fifo_empty_int = (fifo_rptr == fifo_wptr);
    assign fifo_full_int = (fifo_wptr[FIFO_AWIDTH] != fifo_rptr[FIFO_AWIDTH]) &&
                          (fifo_wptr[FIFO_AWIDTH-1:0] == fifo_rptr[FIFO_AWIDTH-1:0]);

    function automatic [FIFO_AWIDTH:0] calculate_fifo_count(
        input [FIFO_AWIDTH:0] wptr,
        input [FIFO_AWIDTH:0] rptr
    );
        if (wptr[FIFO_AWIDTH] == rptr[FIFO_AWIDTH]) begin
            calculate_fifo_count = wptr[FIFO_AWIDTH-1:0] - rptr[FIFO_AWIDTH-1:0];
        end else begin
            calculate_fifo_count = FIFO_DEPTH - rptr[FIFO_AWIDTH-1:0] + wptr[FIFO_AWIDTH-1:0];
        end
    endfunction

    function automatic [FIFO_AWIDTH:0] calculate_fifo_space(
        input [FIFO_AWIDTH:0] wptr,
        input [FIFO_AWIDTH:0] rptr
    );
        calculate_fifo_space = FIFO_DEPTH - calculate_fifo_count(wptr, rptr);
    endfunction

    assign fifo_count_int = calculate_fifo_count(fifo_wptr, fifo_rptr);
    assign fifo_space_int = calculate_fifo_space(fifo_wptr, fifo_rptr);

    // Output assignments
    assign fifo_empty = fifo_empty_int;
    assign fifo_full = fifo_full_int;
    assign fifo_count = fifo_count_int;
    assign fifo_space = fifo_space_int;

    // Event output
    assign event_valid = !fifo_empty_int;
    assign event_type = event_type_fifo[fifo_rptr[FIFO_AWIDTH-1:0]];
    assign event_data = event_data_fifo[fifo_rptr[FIFO_AWIDTH-1:0]];
    assign event_param = event_param_fifo[fifo_rptr[FIFO_AWIDTH-1:0]];

    // Polling trigger generation
    assign polling_trigger = (polling_counter == polling_interval) &&
                            (fifo_space_int >= MAX_BATCH_SIZE);

    // Polling counter
    always @(posedge clk) begin
        if (!rstn) begin
            polling_counter <= 0;
            polling_interval <= POLLING_INTERVAL;
        end else begin
            if (polling_counter < polling_interval) begin
                polling_counter <= polling_counter + 1;
            end else begin
                polling_counter <= 0;
            end
        end
    end

    // FIFO pointer update logic
    always @(posedge clk) begin
        if (!rstn) begin
            fifo_wptr <= 0;
            fifo_rptr <= 0;
        end else begin
            fifo_wptr <= fifo_wptr_next;
            fifo_rptr <= fifo_rptr_next;
        end
    end

    // FIFO write interface functions (to be called by DPI-C)
    function automatic int push_event(
        input bit [EVENT_TYPE_WIDTH-1:0] type_val,
        input bit [EVENT_DATA_WIDTH-1:0] data_val,
        input bit [EVENT_PARAM_WIDTH-1:0] param_val
    );
        if (!fifo_full_int) begin
            event_type_fifo[fifo_wptr[FIFO_AWIDTH-1:0]] = type_val;
            event_data_fifo[fifo_wptr[FIFO_AWIDTH-1:0]] = data_val;
            event_param_fifo[fifo_wptr[FIFO_AWIDTH-1:0]] = param_val;
            fifo_wptr_next = fifo_wptr + 1;
            return 1;
        end else begin
            return 0;
        end
    endfunction

    // Batch event function for better performance
    function automatic int push_event_batch(
        input bit [EVENT_TYPE_WIDTH-1:0] type_array [0:MAX_BATCH_SIZE-1],
        input bit [EVENT_DATA_WIDTH-1:0] data_array [0:MAX_BATCH_SIZE-1],
        input bit [EVENT_PARAM_WIDTH-1:0] param_array [0:MAX_BATCH_SIZE-1],
        input int batch_size
    );
        int pushed_count = 0;
        for (int i = 0; i < batch_size && !fifo_full_int; i++) begin
            if (push_event(type_array[i], data_array[i], param_array[i])) begin
                pushed_count++;
            end else begin
                break;
            end
        end
        return pushed_count;
    endfunction

    // FIFO read logic
    always @(*) begin
        fifo_rptr_next = fifo_rptr;
        if (event_valid && event_ready) begin
            fifo_rptr_next = fifo_rptr + 1;
        end
    end

    // Initialize
    initial begin
        fifo_wptr = 0;
        fifo_rptr = 0;
        fifo_wptr_next = 0;
        fifo_rptr_next = 0;
        polling_counter = 0;
        polling_interval = POLLING_INTERVAL;
    end

endmodule

/**
 * @brief Integrated Event-SHFIFO module with DPI-C interface
 *
 * This module combines the generic Event-SHFIFO hardware with DPI-C interfaces
 * to provide a complete solution that can be easily integrated into existing designs.
 */
module event_sfifo_bfm #(
    parameter INSTANCE_ID = 0,
    parameter FIFO_DEPTH = 8,
    parameter EVENT_TYPE_WIDTH = 32,
    parameter EVENT_DATA_WIDTH = 64,
    parameter EVENT_PARAM_WIDTH = 32,
    parameter POLLING_INTERVAL = 100,
    parameter MAX_BATCH_SIZE = 4
)(
    input  wire clk,
    input  wire rstn,

    // Event output interface
    output wire event_valid,
    output wire [EVENT_TYPE_WIDTH-1:0] event_type,
    output wire [EVENT_DATA_WIDTH-1:0] event_data,
    output wire [EVENT_PARAM_WIDTH-1:0] event_param,
    input  wire event_ready,

    // Status outputs
    output wire fifo_empty,
    output wire fifo_full
);

    localparam FIFO_DEPTH_AWIDTH = $clog2(FIFO_DEPTH);

    // DPI-C function imports
    import "DPI-C" context function void event_sfifo_init(
        input shortint unsigned instance_id,
        input int max_batch_size
    );

    import "DPI-C" context function int event_sfifo_poll_events(
        input shortint unsigned instance_id,
        input int hw_available_space
    );

    // Export functions for software to push events
    export "DPI-C" function h2s_event_sfifo_push_event;
    export "DPI-C" function h2s_event_sfifo_push_event_batch;

    // Unified FIFO instance
    wire polling_trigger;
    wire [FIFO_DEPTH_AWIDTH:0] fifo_space;

    event_sfifo_fifo #(
        .FIFO_DEPTH(FIFO_DEPTH),
        .EVENT_TYPE_WIDTH(EVENT_TYPE_WIDTH),
        .EVENT_DATA_WIDTH(EVENT_DATA_WIDTH),
        .EVENT_PARAM_WIDTH(EVENT_PARAM_WIDTH),
        .POLLING_INTERVAL(POLLING_INTERVAL),
        .MAX_BATCH_SIZE(MAX_BATCH_SIZE)
    ) unified_fifo_inst (
        .clk(clk),
        .rstn(rstn),
        .fifo_empty(fifo_empty),
        .fifo_full(fifo_full),
        .fifo_space(fifo_space),
        .event_valid(event_valid),
        .event_type(event_type),
        .event_data(event_data),
        .event_param(event_param),
        .event_ready(event_ready),
        .polling_trigger(polling_trigger)
    );

    // Polling logic
    always @(posedge clk) begin
        if (rstn) begin
            if (polling_trigger) begin
                event_sfifo_poll_events(INSTANCE_ID, fifo_space);
            end
        end
    end

    // DPI-C functions for software to push events
    function automatic int h2s_event_sfifo_push_event(
        input bit [EVENT_TYPE_WIDTH-1:0] type_val,
        input bit [EVENT_DATA_WIDTH-1:0] data_val,
        input bit [EVENT_PARAM_WIDTH-1:0] param_val
    );
        return unified_fifo_inst.push_event(type_val, data_val, param_val);
    endfunction

    function automatic int h2s_event_sfifo_push_event_batch(
        input bit [EVENT_TYPE_WIDTH-1:0] type_array [0:MAX_BATCH_SIZE-1],
        input bit [EVENT_DATA_WIDTH-1:0] data_array [0:MAX_BATCH_SIZE-1],
        input bit [EVENT_PARAM_WIDTH-1:0] param_array [0:MAX_BATCH_SIZE-1],
        input int batch_size
    );
        return unified_fifo_inst.push_event_batch(
            type_array, data_array, param_array, batch_size);
    endfunction

    // Initialize
    initial begin
        event_sfifo_init(INSTANCE_ID, MAX_BATCH_SIZE);
    end

endmodule 