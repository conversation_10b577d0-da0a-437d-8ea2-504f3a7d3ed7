/**
 * @file mem_sfifo_enhanced_tb.cpp
 * @brief Enhanced Memory SFIFO example with Reference Model and Event Communication
 *
 * This example adds:
 * 1. Reference Model for data comparison
 * 2. Event communication mechanism using event_sfifo module
 * 3. Complete verification flow with data checking
 */

#include "mem_sfifo_wrapper.h"
#include "event_sfifo_wrapper.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <unordered_map>
#include <atomic>

// Event types for software -> hardware communication
enum class EventType {
    TEST_COMPLETE = 0,
    ERROR_DETECTED = 1,
    STATUS_UPDATE = 2
};

// Reference Model for data comparison
class ReferenceModel {
private:
    std::unordered_map<uint64_t, std::vector<uint32_t>> memory_;
    mutable std::mutex memory_mutex_;

public:
    void writeData(uint64_t addr, const std::vector<uint32_t>& data, const std::vector<uint32_t>& mask) {
        std::lock_guard<std::mutex> lock(memory_mutex_);

        // Apply mask and store data
        if (memory_.find(addr) == memory_.end()) {
            memory_[addr] = std::vector<uint32_t>(data.size(), 0);
        }

        for (size_t i = 0; i < data.size() && i < mask.size(); ++i) {
            if (mask[i] == 0xffffffff) {
                memory_[addr][i] = data[i];
            }
        }

        vpi_printf("[RefModel] Write: addr=0x%llx, data[0]=0x%x\n", addr, data[0]);
    }

    std::vector<uint32_t> readData(uint64_t addr) const {
        std::lock_guard<std::mutex> lock(memory_mutex_);

        auto it = memory_.find(addr);
        if (it != memory_.end()) {
            vpi_printf("[RefModel] Read: addr=0x%llx, data[0]=0x%x\n", addr, it->second[0]);
            return it->second;
        } else {
            vpi_printf("[RefModel] Read: addr=0x%llx, data not found (returning zeros)\n", addr);
            return std::vector<uint32_t>(8, 0);  // Default size
        }
    }

    bool compareData(uint64_t addr, const std::vector<uint32_t>& rtl_data) const {
        auto expected = readData(addr);

        if (expected.size() != rtl_data.size()) {
            vpi_printf("[RefModel] ERROR: Size mismatch at addr=0x%llx, expected=%zu, got=%zu\n",
                      addr, expected.size(), rtl_data.size());
            return false;
        }

        for (size_t i = 0; i < expected.size(); ++i) {
            if (expected[i] != rtl_data[i]) {
                vpi_printf("[RefModel] ERROR: Data mismatch at addr=0x%llx[%zu], expected=0x%x, got=0x%x\n",
                          addr, i, expected[i], rtl_data[i]);
                return false;
            }
        }

        vpi_printf("[RefModel] PASS: Data match at addr=0x%llx\n", addr);
        return true;
    }
};

// Enhanced BFM Context with Reference Model and Event handling
class EnhancedBFMContext {
public:
    EnhancedBFMContext(uint16_t bfm_id, svScope bfm_scope, uint16_t max_batch_size)
        : id(bfm_id)
        , scope(bfm_scope)
        , scope_name(svGetNameFromScope(bfm_scope))
        , is_valid(true)
        , pending_reads_(0)
        , completed_reads_(0)
        , error_count_(0) {

        // Register with the SFIFO manager
        std::string scope_str = scope_name;
        sfifo_wrapper = MemSfifoManager::getInstance().registerInstance(
            bfm_id, scope_str, max_batch_size
        );

        // Register with the Event SFIFO manager
        event_sfifo_wrapper = EventSfifoManager::getInstance().registerInstance(
            bfm_id, scope_str, max_batch_size
        );
    }

    // Add write request and update reference model
    void addWriteRequest(uint64_t addr, const std::vector<uint32_t>& data,
                        const std::vector<uint32_t>& mask) {
        if (sfifo_wrapper) {
            // Add to hardware FIFO
            sfifo_wrapper->addWriteRequest(addr, data, mask);
            vpi_printf("[Enhanced BFM Context %d] Added write request: addr=0x%llx, pending=%d\n",
                      id, addr, sfifo_wrapper->getFifoSize());
            for (int i = 0; i < data.size(); i++) {
                vpi_printf("[Enhanced BFM Context %d] Write data[%d]=0x%x\n",
                          id, i, data[i]);
            }
            for (int i = 0; i < mask.size(); i++) {
                vpi_printf("[Enhanced BFM Context %d] Write mask[%d]=0x%x\n",
                          id, i, mask[i]);
            }

            // Update reference model
            ref_model.writeData(addr, data, mask);
        }
    }

    // Add read request and track pending reads
    void addReadRequest(uint64_t addr) {
        if (sfifo_wrapper) {
            sfifo_wrapper->addReadRequest(addr);
            pending_reads_++;
            vpi_printf("[Enhanced BFM Context %d] Added read request: addr=0x%llx, pending=%d\n",
                      id, addr, pending_reads_.load());
        }
    }

    // Handle read response from RTL
    void handleReadResponse(uint64_t addr, const std::vector<uint32_t>& rtl_data) {
        // Compare with reference model
        bool match = ref_model.compareData(addr, rtl_data);

        completed_reads_++;
        if (!match) {
            error_count_++;
        }

        vpi_printf("[Enhanced BFM] Read response: addr=0x%llx, match=%s, completed=%d/%d, errors=%d\n",
                  addr, match ? "PASS" : "FAIL", completed_reads_.load(), pending_reads_.load(), error_count_.load());

        // Check if test is complete
        if (completed_reads_ >= pending_reads_ && pending_reads_ >= 10) {
            // All reads completed, signal test completion
            if (error_count_ == 0) {
                event_sfifo_wrapper->addEvent(static_cast<uint32_t>(EventType::TEST_COMPLETE), 0, 0);
                vpi_printf("[Enhanced BFM] TEST PASSED: All %d reads completed successfully!\n", completed_reads_.load());
            } else {
                event_sfifo_wrapper->addEvent(static_cast<uint32_t>(EventType::ERROR_DETECTED), error_count_, completed_reads_);
                vpi_printf("[Enhanced BFM] TEST FAILED: %d errors out of %d reads\n", error_count_.load(), completed_reads_.load());
            }
        }
    }

    // Get FIFO size for monitoring
    size_t getFifoSize() const {
        return sfifo_wrapper ? sfifo_wrapper->getFifoSize() : 0;
    }

    // Get test status
    bool isTestComplete() const {
        return completed_reads_ >= pending_reads_ && pending_reads_ > 0;
    }

    int getErrorCount() const {
        return error_count_;
    }

public:
    uint16_t id;
    svScope scope;
    std::string scope_name;
    bool is_valid;
    MemSfifoWrapper* sfifo_wrapper;
    EventSfifoWrapper* event_sfifo_wrapper;
    ReferenceModel ref_model;

private:
    std::atomic<int> pending_reads_;
    std::atomic<int> completed_reads_;
    std::atomic<int> error_count_;
};

// Enhanced BFM Manager
class EnhancedBFMManager {
public:
    std::vector<std::unique_ptr<EnhancedBFMContext>> contexts_;

private:
    uint32_t num_instances_;

public:
    EnhancedBFMManager() : num_instances_(0) {}

    void initBFM(uint16_t id, uint32_t page_size_in_bit,
                uint64_t addr_width, uint16_t data_width,
                uint32_t cont_line_per_channel,
                uint8_t merged_channel_num,
                uint16_t max_batch_size) {

        vpi_printf("[Enhanced BFM] Initializing BFM with ID=%d\n", id);

        svScope scope = svGetScope();
        if (!scope) {
            vpi_printf("Error: Could not get scope in initBFM\n");
            return;
        }

        // Create enhanced context with reference model and event handling
        auto ctx = std::make_unique<EnhancedBFMContext>(id, scope, max_batch_size);
        contexts_.push_back(std::move(ctx));
        num_instances_++;

        vpi_printf("[Enhanced BFM] BFM instance created with ID=%d, scope=%s\n",
                   id, svGetNameFromScope(scope));
    }

    EnhancedBFMContext* findContext(uint16_t id) {
        for (auto& ctx : contexts_) {
            if (ctx->is_valid && ctx->id == id) {
                return ctx.get();
            }
        }
        return nullptr;
    }

    // Enhanced test function with reference model
    void runEnhancedTest() {
        vpi_printf("[Enhanced BFM] Starting enhanced test with Reference Model\n");

        for (auto& ctx : contexts_) {
            if (!ctx->is_valid) continue;

            // Test write operations (data will be stored in reference model)
            for (int i = 0; i < 10; i++) {
                uint64_t addr = 0x80000000 + i * 0x20;
                std::vector<uint32_t> data = {
                    static_cast<uint32_t>(0x12345600 + i), 0x90abcdef, 0x12345678, 0x90abcdef,
                    0x12345678, 0x90abcdef, 0x12345678, 0x90abcdef
                };
                std::vector<uint32_t> mask = {0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff,
                                             0xffffffff, 0xffffffff, 0xffffffff, 0xffffffff};

                // Add write request (updates both RTL and reference model)
                ctx->addWriteRequest(addr, data, mask);

                vpi_printf("[Enhanced BFM] Added write request %d, FIFO size: %zu\n",
                          i, ctx->getFifoSize());
            }

            // Wait a bit for writes to complete
            std::this_thread::sleep_for(std::chrono::milliseconds(100));

            // Test read operations (will be compared with reference model)
            for (int i = 0; i < 10; i++) {
                uint64_t addr = 0x80000000 + i * 0x20;

                // Add read request (will trigger comparison when response comes back)
                ctx->addReadRequest(addr);

                vpi_printf("[Enhanced BFM] Added read request %d, FIFO size: %zu\n",
                          i, ctx->getFifoSize());
            }
        }

        vpi_printf("[Enhanced BFM] Enhanced test requests submitted\n");
    }

    static EnhancedBFMManager& getInstance() {
        static EnhancedBFMManager instance;
        return instance;
    }
};

// DPI-C functions for enhanced functionality
extern "C" {
    // Enhanced initialization function
    void enhanced_init_bfm(
        uint16_t id,
        uint32_t page_size_in_bit,
        uint64_t addr_width,
        uint16_t data_width,
        uint32_t cont_line_per_channel,
        uint8_t merged_channel_num,
        uint16_t max_batch_size
    ) {
        vpi_printf("[Enhanced BFM] enhanced_init_bfm called with id=%d\n", id);
        EnhancedBFMManager::getInstance().initBFM(id, page_size_in_bit, addr_width,
                                                 data_width, cont_line_per_channel,
                                                 merged_channel_num, max_batch_size);
    }

    // Enhanced test function
    void enhanced_start_test() {
        vpi_printf("[Enhanced BFM] Starting enhanced test\n");

        std::thread test_thread([&]() {
            // Wait for initialization
            std::this_thread::sleep_for(std::chrono::milliseconds(100));

            EnhancedBFMManager::getInstance().runEnhancedTest();

            // Monitor test progress
            for (int i = 0; i < 200; i++) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));

                // Check if all tests are complete
                bool all_complete = true;
                auto& manager = EnhancedBFMManager::getInstance();
                for (auto& ctx : manager.contexts_) {
                    if (!ctx->isTestComplete()) {
                        all_complete = false;
                        break;
                    }
                }

                if (all_complete) {
                    vpi_printf("[Enhanced BFM] All tests completed!\n");
                    break;
                }
            }
        });

        test_thread.detach();
    }

    // Read data response handler (called by RTL)
    void h2s_read_data_resp(
        uint16_t instance_id,
        uint64_t addr,
        const uint32_t* data,
        int data_words
    ) {
        EnhancedBFMContext* ctx = EnhancedBFMManager::getInstance().findContext(instance_id);
        if (ctx) {
            std::vector<uint32_t> rtl_data(data, data + data_words);
            ctx->handleReadResponse(addr, rtl_data);
        } else {
            vpi_printf("[Enhanced BFM] ERROR: Context not found for instance_id=%d\n", instance_id);
        }
    }

    // Get enhanced status
    void enhanced_get_status(uint16_t id, int* fifo_size,
                           int* test_complete, int* error_count) {
        EnhancedBFMContext* ctx = EnhancedBFMManager::getInstance().findContext(id);
        if (ctx) {
            *fifo_size = static_cast<int>(ctx->getFifoSize());
            *test_complete = ctx->isTestComplete() ? 1 : 0;
            *error_count = ctx->getErrorCount();
        } else {
            *fifo_size = -1;
            *test_complete = -1;
            *error_count = -1;
        }
    }
}
