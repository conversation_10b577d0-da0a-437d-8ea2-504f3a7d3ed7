# gfifo Architecture Specification (消息队列帧协议版)

## 1. 设计目标 / Design Goals

- **DPI-C函数调用抽象为消息帧**，统一进入消息队列（FIFO），支持异步、分片、批量传输。
- **适配emulator环境**，规避DPI-C参数/带宽/同步限制。
- **自动生成中间层代码**，用户原生SystemVerilog/C代码无需修改。
- **高效、可扩展**，支持任意参数宽度、函数数量、异步/稀疏调用。

---

## 2. 消息帧协议 / Message Frame Protocol

每一帧结构如下：

| 字段      | 宽度      | 说明                         |
|-----------|-----------|------------------------------|
| func_id   | 8         | 函数编号                     |
| msg_len   | 16        | 本消息总bit数（不含头）       |
| data      | 可变      | 参数打包数据（可分多帧）      |
| eom       | 1         | 1=消息结束，0=未结束（分片）  |

- **单帧**：eom=1，消息一次传完。
- **多帧**：eom=0，后续帧继续，直到eom=1。
- **帧最大长度**（如1024bit）可配置。

---

## 3. JSON描述 / JSON Description

自动生成的JSON文件描述所有DPI-C函数、参数、帧头格式。例如：

```json
{
  "functions": [
    {
      "id": 0,
      "name": "h2s_func0",
      "params": [
        { "name": "msg",  "width": 16 },
        { "name": "data", "width": 14 }
      ]
    },
    {
      "id": 1,
      "name": "h2s_func1",
      "params": [
        { "name": "msg",   "width": 24 },
        { "name": "valid", "width": 1 }
      ]
    },
    {
      "id": 2,
      "name": "h2s_func2",
      "params": [
        { "name": "msg",  "width": 128 },
        { "name": "data", "width": 256 }
      ]
    },
    {
      "id": 3,
      "name": "h2s_func3",
      "params": [
        { "name": "addr", "width": 256 },
        { "name": "data", "width": 2048 }
      ]
    }
  ],
  "frame_header": {
    "func_id_width": 8,
    "msg_len_width": 16,
    "eom_width": 1
  },
  "max_frame_bits": 1024
}
```

---

## 4. 硬件侧实现 / SystemVerilog Side Implementation

- **所有DPI-C函数调用**都转为一条消息帧，写入统一FIFO。
- 每帧=func_id+msg_len+data+eom，参数按json描述打包。
- FIFO满、阈值、或周期性flush时，批量送到软件侧。
- 超长参数自动分片为多帧，eom=0/1标记消息结束。

**伪代码：**
```systemverilog
// 统一FIFO定义
bit [MAX_FRAME_BITS-1:0] fifo_mem [DEPTH-1:0];

// DPI-C函数调用时：
function void h2s_funcX(...);
  // 打包参数，写func_id, msg_len, data, eom
  // 写入fifo_mem
endfunction

// flush时批量送出
if (fifo_ready_to_flush) begin
  dpi_send(fifo_mem, ...);
end
```

---

## 5. 软件侧实现 / C/C++ Side Implementation

- 循环解析收到的帧包，读取func_id、msg_len、data、eom。
- 按func_id查json，累积分片，eom=1时重组完整消息。
- 拆包参数，调用原生C函数指针。

**伪代码：**
```cpp
while (fifo_not_empty) {
    func_id = ...;
    msg_len = ...;
    data = ...;
    eom = ...;
    buffer[func_id].append(data);
    if (eom) {
        // 查json，拆包参数
        call_user_func(func_id, ...);
        buffer[func_id].clear();
    }
}
```

---

## 6. 示例 / Example

### 用户原始DPI-C声明
```systemverilog
import "dpi-c" void function h2s_func0(bit [15:0] msg, bit [13:0] data);
import "dpi-c" void function h2s_func1(bit [23:0] msg, bit valid);
import "dpi-c" void function h2s_func2(bit [127:0] msg, bit [255:0] data);
import "dpi-c" void function h2s_func3(bit [255:0] addr, bit [2047:0] data);
```

### 自动生成的JSON
（见第3节）

### 帧格式
| func_id | msg_len | data bits... | eom |
|---------|---------|--------------|-----|
|   8b    |  16b    |  ...         | 1b  |

---

## 7. 用户须知 / User Notes

- 所有DPI-C函数参数必须为input。
- DPI-C调用在emulator上为异步、非阻塞，不能依赖其同步副作用。
- 合并/分片/异步传输由中间层自动处理，对用户透明。
- 超长参数自动分片，软件侧自动重组。

---

## 8. 工程组织建议 / Project Structure

```
gfifo/
  |-- tools/                  # 代码生成工具
  |     |-- gen_gfifo_desc.py
  |-- desc/                   # json描述
  |     |-- gfifo_functions.json
  |-- rtl/                    # 自动生成的SV中间层
  |     |-- gfifo_msg_queue.sv
  |-- sw/                     # 自动生成的C中间层
  |     |-- gfifo_unpack.c/h
  |-- archi_spec.md
```

---

## 9. 重要硬件约束 / Important Hardware Constraints

- **单周期写入限制**：在硬件实现中，每个cycle只能向FIFO的一个地址写入一笔数据。
  - 例如，若FIFO位宽为1024bit，则每拍最多写入1024bit。
  - 10拍内最多写入1024*10=10240bit。
- **总量限制**：所有DPI-C函数在N拍内要写入的数据总量不能超过FIFO最大容量，否则会丢失或阻塞。
- **设计建议**：
  - 编译期或仿真前应评估所有函数在高峰期的最大数据量，确保不会超出FIFO带宽和容量。
  - 如有超长消息，建议分片、分周期写入。

// In hardware, only one data word can be written to the FIFO per cycle (e.g., 1024 bits per cycle for a 1024-bit FIFO). The total data written by all DPI-C functions in N cycles must not exceed the FIFO's maximum capacity, otherwise data loss or backpressure may occur.

---