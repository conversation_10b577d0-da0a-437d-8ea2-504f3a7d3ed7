#!/usr/bin/env python3
import json
import os

# 输入输出路径
JSON_PATH = '../examples/gfifo_ports.json'
SV_PATH = '../rtl/gfifo_port_desc_pkg.sv'
C_PATH = '../sw/gfifo_port_desc.h'

# 读取JSON
with open(JSON_PATH, 'r') as f:
    desc = json.load(f)

ports = desc['ports']
max_packet_bits = desc['max_packet_bits']

# 生成SystemVerilog头文件
def gen_sv():
    lines = []
    lines.append('// Auto-generated by gen_gfifo_desc.py')
    lines.append('package gfifo_port_desc_pkg;')
    lines.append(f'  parameter int GFIFO_MAX_PACKET_BITS = {max_packet_bits};')
    lines.append(f'  parameter int GFIFO_NUM_PORTS = {len(ports)};')
    lines.append('  typedef struct packed { bit [15:0] port_id; bit [15:0] bit_offset; bit [15:0] bit_width; } gfifo_port_desc_t;')
    lines.append("  localparam gfifo_port_desc_t GFIFO_PORT_DESC[GFIFO_NUM_PORTS] = '{")
    for i, p in enumerate(ports):
        s = f"    '{{16'd{p['port_id']}, 16'd{p['bit_offset']}, 16'd{p['bit_width']}}}'"
        if i != len(ports) - 1:
            s += ','
        lines.append(s)
    lines.append('  };')
    lines.append('endpackage')
    os.makedirs(os.path.dirname(SV_PATH), exist_ok=True)
    with open(SV_PATH, 'w') as f:
        f.write('\n'.join(lines) + '\n')

# 生成C头文件
def gen_c():
    lines = []
    lines.append('// Auto-generated by gen_gfifo_desc.py')
    lines.append('#pragma once')
    lines.append('#include <stdint.h>')
    lines.append('typedef struct { uint16_t port_id; uint16_t bit_offset; uint16_t bit_width; } gfifo_port_desc_t;')
    lines.append(f'#define GFIFO_MAX_PACKET_BITS {max_packet_bits}')
    lines.append(f'#define GFIFO_NUM_PORTS {len(ports)}')
    lines.append('static const gfifo_port_desc_t gfifo_port_desc[GFIFO_NUM_PORTS] = {')
    for i, p in enumerate(ports):
        s = f'    {{ {p["port_id"]}, {p["bit_offset"]}, {p["bit_width"]} }}'
        if i != len(ports) - 1:
            s += ','
        lines.append(s)
    lines.append('};')
    os.makedirs(os.path.dirname(C_PATH), exist_ok=True)
    with open(C_PATH, 'w') as f:
        f.write('\n'.join(lines) + '\n')

if __name__ == '__main__':
    gen_sv()
    gen_c()
    print(f'Generated {SV_PATH} and {C_PATH}') 