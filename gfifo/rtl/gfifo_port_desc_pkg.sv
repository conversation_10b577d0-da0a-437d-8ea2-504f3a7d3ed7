// Auto-generated by gen_gfifo_desc.py
package gfifo_port_desc_pkg;
  parameter int GFIFO_MAX_PACKET_BITS = 1024;
  parameter int GFIFO_NUM_PORTS = 3;
  typedef struct packed { bit [15:0] port_id; bit [15:0] bit_offset; bit [15:0] bit_width; } gfifo_port_desc_t;
  localparam gfifo_port_desc_t GFIFO_PORT_DESC[GFIFO_NUM_PORTS] = '{
    '{16'd0, 16'd0, 16'd32}',
    '{16'd1, 16'd32, 16'd64}',
    '{16'd2, 16'd96, 16'd16}'
  };
endpackage
